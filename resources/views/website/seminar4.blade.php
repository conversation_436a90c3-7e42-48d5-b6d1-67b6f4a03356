@extends('layouts.master')

@section('title' , $seminar->title)

@section('content')

    <div>

        {{--title--}}

        @if($seminar->id !== 43 && $seminar->title)

            <!--<div class="container">-->

            <!--    <div class="row">-->

            <!--        @include('partials.success')-->
            <!--        @include('partials.failed')-->
            <!--        @include('partials.validation_errors')-->

            <!--        <div style="margin: 40px 0" class="text-center">-->

            <!--            <h1>{{ isset($title) ? $title : $seminar->title }}</h1>-->

            <!--        </div>-->

            <!--    </div>-->

            <!--</div>-->

        @endif

        {{--desgin--}}

        <div class="text-center">
            @include('partials.success')
        </div>
        @include('partials.failed')

        <div>

            <img style="width: 100%" src="{{ asset('imgs/' . $logo) }}" />

        </div>

        {{--register--}}

        <div style="background-color: #EEEEEE;margin-bottom: 20px">

            <div>

                {{ Form::open(['route' => 'participant_register']) }}

                {{ Form::hidden('seminar_id' , $seminar->id) }}

                <div class="container">

                    <div class="row">

                        <div style="border-bottom: 1px solid #000000" class="page-header">
                            <h1>سجل الأن</h1>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('first_name' , '* الأسم الاول : ') }}</h4>
                                {{ Form::text('first_name' , null , ['class' => 'form-control' , 'id' => 'first_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('last_name' , '* أسم العائلة : ') }}</h4>
                                {{ Form::text('last_name' , null , ['class' => 'form-control' , 'id' => 'last_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('country' , 'الدولة : ') }}</h4>
                                <select name="country" class="form-control">
                                    @foreach($countries as $country)
                                        <option {{ isset($location) && $country == $location ? 'selected' : null }} value="{{ $country }}">{{ $country }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                    </div>

                    <div class="row" style="display: flex;align-items: flex-end">

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('email' , 'البريد الألكترونى : ') }}</h4>
                                {{ Form::email('email' , null , ['class' => 'form-control' , 'id' => 'email' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('phone' , '* الهاتف : ') }}</h4>
                                {{ Form::number('phone' , null , ['class' => 'form-control' , 'id' => 'phone' , 'required' => true]) }}
                            </div>
                        </div>

                        @if($seminar->id == 51)

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>أختار الندوة : </label>
                                    <select name="seminar" class="form-control">
                                        <option value="دبي">
                                            دبى
                                        </option>
                                        <option value="العين">
                                            العين
                                        </option>
                                    </select>
                                </div>
                            </div>

                        @endif

                    </div>
                    
                    <div class="row" style="display: flex;align-items: flex-end">
                    
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ Form::submit('سجل'  , ['class' => 'btn btn-success form-control']) }}
                            </div>
                        </div>
                        
                    </div>

                </div>

                {{ Form::close() }}

            </div>

        </div>

        {{--contents--}}

        @if(count($seminar->contents))

            <div class="container">

                <div class="row">

                    <div class="col-sm-12">

                        <h1 style="margin-bottom: 20px">فى هذه الندوة ستتعرف على : </h1>

                        <ul>

                            @foreach($seminar->contents as $content)

                                <li>
                                    <p class="lead">
                                        {{ $content }}
                                    </p>
                                </li>

                            @endforeach

                        </ul>

                    </div>

                </div>

            </div>

        @endif


        {{--map--}}


            @if($seminar->id == 45)

                <hr />

                <div class="container">

                    <div class="row">

                        <div class="col-sm-12">

                            <h1>العنوان</h1>

                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3578.4656967094115!2d50.57509271550205!3d26.24654507927085!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e49a5ed6ae6e27b%3A0xafdfa35ad10ec176!2sWyndham+Grand+Manama!5e0!3m2!1sen!2sbh!4v1542627077594" width="100%" height="450" frameborder="0" style="border:0" allowfullscreen></iframe>

                        </div>

                    </div>

                </div>

            @endif


        {{--footer--}}

        <div style="background-color: black">

            <div class="container">

                <div class="row">

                    <div class="col-sm-12">

                        <p style="color: #FFFFFF;padding: 50px 0 30px 0;" class="lead">

                            {{--يحتوي تداول العملات وعقود الفروقات على مخاطر. الخسائر يمكن ان تتجاوز الودائع. رقم التعريف المؤسسي لون فيننشال ماركتس أستراليا 00126577--}}

                            <!--78٪ من حسابات المستثمرين الأفراد يخسرون عند تداول العقود مقابل الفروقات مع هذه الأداة. يجب أن تفكر فيما إذا كنت تفهم كيف تعمل العقود مقابل الفروقات وفيما إذا كنت تستطيع تحمل مخاطر عالية تسبب فقدان أموالك.-->

التداول بالعقود مقابل الفروقات و العملات العالمية بنظام الهامش يحمل مخاطر عالية.

                        </p>

                    </div>

                </div>

            </div>

        </div>

    </div>

@endsection