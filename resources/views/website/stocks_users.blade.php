<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs4/jq-3.3.1/dt-1.10.18/b-1.5.4/b-flash-1.5.4/datatables.min.css"/>
    <title>دورة لتعلم أساسيات التداول فى البورصات العالمية</title>
</head>
<body>

<div id="app">

    <div class="container">

        <table v-if="users.length > 0" id="table" class="table table-striped table-bordered">

            <thead>

                <tr>
                    <th>ID</th>
                    <th>name</th>
                    <th>phone</th>
                    <th>address</th>
                    <th>age</th>
                    <th>interested</th>
                    <th>Date</th>
                </tr>

            </thead>

            <tbody>

                <tr v-for="(user, index) in users">
                    <th>@{{ index + 1 }}</th>
                    <th>@{{ user.name }}</th>
                    <th>@{{ user.phone }}</th>
                    <th>@{{ user.address }}</th>
                    <th>@{{ user.age }}</th>
                    <th>@{{ user.interested }}</th>
                    <th>@{{ user.date }}</th>
                </tr>

            </tbody>

        </table>

    </div>

</div>

<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/vue@2.5.21/dist/vue.js"></script>
<script src="https://www.gstatic.com/firebasejs/5.7.0/firebase.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vee-validate@latest/dist/vee-validate.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/v/bs/jq-3.3.1/jszip-2.5.0/dt-1.10.18/b-1.5.2/b-html5-1.5.2/datatables.min.js"></script><script>

    Vue.use(VeeValidate);

    var config = {
        apiKey: "AIzaSyA_HIQyGStTCgxlKd537PcsKtbs89GlS7k",
        authDomain: "meetup-app-b7b35.firebaseapp.com",
        databaseURL: "https://meetup-app-b7b35.firebaseio.com",
        projectId: "meetup-app-b7b35",
        storageBucket: "meetup-app-b7b35.appspot.com",
        messagingSenderId: "21277398310"
    };

    firebase.initializeApp(config);

    var db = firebase.firestore();

    db.settings({
        timestampsInSnapshots: true
    });

    var app = new Vue({

        el: '#app',

        data: {
            users : []
        } ,

        mounted: function () {

            var self = this;

            db.collection("users").orderBy('date', 'desc').get().then(function (querySnapshot) {
                querySnapshot.forEach(function (doc) {
                    self.users.push(doc.data());
                });
                console.log(self.users);
                setTimeout(function () {
                    $('#table').DataTable({
                        dom: 'Bfrtip',
                        buttons: [
                            {
                                extend: 'excel',
                                text: 'Excel File' ,
                                className : 'btn btn-success'
                            }
                        ]
                    });
                },1000)
            });

        }

    });

</script>

</body>
</html>