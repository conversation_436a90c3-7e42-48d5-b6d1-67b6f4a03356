@extends('layouts.master')

@section('title' , 'الندوات')

@section('style')

    <style>

        .table-container {
            overflow-x: scroll;
            margin-bottom: 40px;
        }

    </style>

@endsection

@section('content')

    <div class="container">

        @if ($seminar)

            <div class="alert alert-info">

                <strong>رابط الندوة : </strong>

                <a href="{{ $seminar->url }}">
                    {{ $seminar->url }}
                </a>

            </div>

            <div class="table-container">

                <table class="table table-bordered">

                    <tr>
                        <th>
                            عنوان الندوة
                        </th>
                        <th>
                            مكان الندوة
                        </th>
                        <th>
                            تاريخ الندوة
                        </th>
                        <th>
                            وقت الندوة
                        </th>
                        <th>
                            محتويات الندوة
                        </th>

                    </tr>

                    <tr>
                        <td>
                            {{ $seminar->title }}
                        </td>
                        <td>
                            {{ $seminar->place }}
                        </td>
                        <td>
                            {{ $seminar->date }}
                        </td>
                        <td>
                            {{ $seminar->time }}
                        </td>
                        <td>
                            <ul>
                                @foreach($seminar->contents as $content)
                                    <li>{{ $content }}</li>
                                @endforeach
                            </ul>
                        </td>

                    </tr>

                </table>

            </div>

            @if(count($seminar->participants))

                <div class="page-header">
                    <h3>
                        المشتركين بالندوة
                        <a class="btn btn-success" href="{{ route('excel.export' , ['participant' , $seminar->id]) }}">
                            <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
                            ملف الأكسيل
                        </a>
                    </h3>
                </div>

                <div class="table-container">

                    <table class="table table-bordered">

                        <tr>
                            <th>
                                الرقم
                            </th>
                            <th>
                                الأسم الأول
                            </th>

                            <th>
                                الأسم الأخير
                            </th>
                            <th>
                                الدولة
                            </th>
                            <th>
                                البريد الألكترونى
                            </th>
                            <th>
                                رقم الهاتف
                            </th>
                            <th>
                                الندوة
                            </th>
                        </tr>

                        <?php $counter = 1 ?>

                        @foreach($seminar->participants as $participant)

                            <tr>
                                <td>
                                    {{ $counter++ }}
                                </td>
                                <td>
                                    {{$participant->first_name}}
                                </td>
                                <td>
                                    {{$participant->last_name}}
                                </td>
                                <td>
                                    {{$participant->country}}
                                </td>
                                <td>
                                    {{$participant->email}}
                                </td>
                                <td>
                                    {{$participant->phone}}
                                </td>
                                <td>
                                    {{ $participant->seminar }}
                                </td>
                            </tr>

                        @endforeach

                    </table>

                </div>

            @endif

        @endif

    </div>

@endsection