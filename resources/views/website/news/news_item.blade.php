@php
    $url = urldecode(route('website.news.news_item' , ['id' => $newsItem->id , 'title' => $newsItem->title]));
    $description = $newsItem->title;
@endphp

@extends('website.news.master')
@section('title' , $newsItem->title)
@section('meta_data')

    <meta name="description" content="{{ $description }}" />
    <link rel="original-source" href="{{ $url }}" />
    <link rel="amphtml" href="{{ $url }}" />

    <link rel="canonical" href="{{ $url }}" />
    <meta property="og:locale" content="ar_Ar" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="{{ $newsItem->title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="viseminars" />
    <meta property="article:tag" content="اخبار البورصة المصرية" />
    <meta property="article:tag" content="اخبار الاقتصاد" />
    <meta property="article:section" content="اقتصاد وبورصة" />
    <meta property="og:image" content="{{ $newsItem->image }}" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:title" content="{{ $newsItem->title }}" />
    <meta name="twitter:image" content="{{ $newsItem->image }}" />

@endsection

@section('content')

    <div class="container">

        <div class="panel panel-default">

            <div class="panel-body">

                <div class="page-header">

                    <h1 style="color: #4578c6" class="text-center">
                        {{ $newsItem->title }}
                    </h1>

                </div>

                <div>

                    <div style="margin-bottom: 20px" class="text-center">

                        <div style="max-width: 500px; margin: 0 auto">

                            <img class="img-responsive" src="{{ $newsItem->image }}">

                        </div>

                    </div>

                </div>

                <div class="news-body">

                    <p class="lead">

                        {{ $newsItem->content }}

                    </p>

                </div>


            </div>

        </div>

    </div>

@endsection