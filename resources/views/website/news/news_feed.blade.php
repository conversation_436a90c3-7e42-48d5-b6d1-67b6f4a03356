@extends('website.news.master')
@section('title' , 'الأخبار')
@section('content')

    <div class="page-header news-title">

        <h1 class="text-center">الأخبار</h1>

    </div>

    <div class="container">

        @if(count($news))

            <div class="news-list-container">

                @foreach($news as $news_item)

                    <div class="panel panel-default">
                        <div class="panel-body">

                            <div class="media">
                                <div class="media-left">
                                    <a href="#">
                                        <img class="media-object news-image" src="{{ $news_item->image }}" alt="...">
                                    </a>
                                </div>
                                <div class="media-body">
                                    <h4 class="media-heading">
                                        <a href="{{ route('website.news.news_item' , ['id' => $news_item->id , 'title' => str_replace(' ' , '-' , $news_item->title )]) }}">
                                            {{ $news_item->title }}
                                        </a>
                                    </h4>
                                    <p>
                                        بتاريخ :
                                        {{ $news_item->created_at->toDayDateTimeString() }}
                                    </p>
                                </div>
                            </div>

                        </div>
                    </div>

                @endforeach

                <div class="text-center">

                    <ul class="pagination">

                        {{ $news->links() }}

                    </ul>

                </div>

            </div>

        @endif

    </div>

@endsection