<?php

$lang = 'ar';

function en_ar ($en , $ar) {

    $lang = 'ar';

    if ($lang == 'en') {
        return $en;
    }

    if ($lang == 'ar') {
        return $ar;
    }

}

?>

        <!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="https://fonts.googleapis.com/css?family=Tajawal" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}" />

    @if($lang == 'ar')

        <link rel="stylesheet" href="{{ asset('css/bootstrap-rtl.css') }}" />

    @endif

    <link rel="stylesheet" href="{{ asset('css/TimeCircles.css') }}">
    <title>Vi Markets</title>

    <style>

        body {
            margin: 0;
            /*color: #FFFFFF;*/
            font-family: 'Tajawal', sans-serif;
        }

        .app-container {
            background-color: #EEEEEE;
            color: #000000;
        }

        h1 , h2 , h3 {
            margin: 0;
        }

        .header {
            background-image: url(https://cdn-resources.onefinancialmarkets.com/vimarkets/sites/vimarkets/files/750-VI-Markets-HP-banners-v1c.jpg);
            width: 1351px;
            height: 353px;
            padding: 50px;
        }

        .top-logos-container {
            padding-top: 50px;
        }

        .table-container {
            padding-top: 20px;
        }

        .table-cell {
            padding: 5px 20px;
            text-align: center;
        }

        .th {
            background-color: #038897;;
        }

        .td {
            background-color: #ffffff;
            color: #000000;
            font-weight: bold;
        }

        .form-container {
            background-color: #ffffff;
            border-radius: 4px;
            color: #000000;
            padding: 20px;
        }

    </style>

</head>

<body>

<div>

    <div>

        @include('partials.validation_errors')
        @include('partials.success')
        @include('partials.failed')

        {{--<div class="row">--}}

        {{--<div class="col-sm-6">--}}

        {{--<img src="{{ asset('imgs/vimarkets_logo.png') }}">--}}

        {{--</div>--}}

        {{--<div class="col-sm-6">--}}

        {{--@include('partials.success')--}}

        {{--<br><br>--}}

        {{--<h1>VI MARKETS</h1>--}}

        {{--<br>--}}

        {{--@if($lang == 'ar')--}}

        {{--<h3>وسيطك المالي العالمي&nbsp;</h3>--}}
        {{--<h3>مرخص و منظم من FCA</h3>--}}
        {{--<h3>تحت الرقم: 672736</h3>--}}

        {{--@else--}}

        {{--<h3>Your Global Financial Broker</h3>--}}
        {{--<h3>Licensed and regulated by FCA</h3>--}}
        {{--<h3>Under the number: 672736</h3>--}}

        {{--@endif--}}


        {{--</div>--}}

        {{--</div>--}}

        <img style="width: 100%" src="{{ asset('imgs/vihome_bg.jpg') }}">

    </div>


    <div class="container">

        <div class="page-header">

            <h1>
                {{
                    en_ar(
                        '
                            Contact Us
                        ' ,
                        '
                            تواصل معنا
                        '
                    )
                }}
            </h1>

        </div>

        <div class="row">

            <div class="col-sm-6">

                <p style="margin-bottom: 10px" class="lead">
                    {{
                        en_ar(
                            'Kuwait Office' , 'مكتب الكويت'
                        )
                    }}
                </p>

                <p class="lead">
                    VI Markets
                </p>

                <p>Sharq - Mazaya Tower 02 - 10th floor</p>

            </div>

            <div class="col-sm-6">

                <p>PO BOX 3040
                </p>

                <p>22031, </p>

                <p>Salmiya, Kuwait
                </p>

                <p>
                    <strong>T:</strong> + 965 22256988
                </p>

                <p>
                    <strong>
                        E:
                    </strong>
                    <EMAIL>
                </p>

            </div>

        </div>

    </div>


    <div class="app-container">

        <div class="container">

            <div class="row">

                <div class="col-sm-8 col-sm-offset-2">

                    <div style="border-bottom: 1px solid black" class="page-header">

                        <h1>
                            {{
                                en_ar(
                                    'Regsiter Now' ,
                                    'سجل الأن'
                                )
                            }}
                        </h1>

                    </div>

                    @include('partials.validation_errors')

                    <form method="post" action="{{ route('website.contact_us.post_contact_us_form') }}" style="margin-top: 20px">

                        {{ csrf_field() }}

                        <input type="hidden" name="form_id" value="1">

                        <input type="hidden" name="lang" value="{{ $lang }}">

                        <div class="form-group">

                            <label for="name">
                                <p class="lead">
                                    <strong>* {{ en_ar('Name' , 'الأسم') }} :</strong>
                                </p>
                            </label>

                            <input name="name" type="text" value="{{ old('name') }}" class="form-control" id="name">

                        </div>

                        <div class="form-group">

                            <label for="email">
                                <p class="lead">
                                    <strong>
                                        {{ en_ar('Email address' , 'البريد الألكترونى') }} :
                                    </strong>
                                </p>
                            </label>

                            <input name="email" value="{{ old('email') }}" type="email" class="form-control" id="email">

                        </div>

                        <div class="form-group">

                            <label for="phone">
                                <p class="lead">
                                    <strong>
                                        * {{ en_ar('phone' , 'الهاتف') }} :
                                    </strong>
                                </p>
                            </label>

                            <input name="phone" value="{{ old('phone') }}" type="text" class="form-control" id="phone">

                        </div>

                        <!--<div class="form-group">-->

                    <!--    <label for="message">{{ en_ar('Message' , 'الرسالة') }} :</label>-->

                    <!--    {{ Form::textarea('message' , null , ['class' => 'form-control']) }}-->

                        <!--</div>-->

                        <div style="margin-top: 30px; margin-bottom: 0" class="form-group">

                            <div class="row">

                                <div class="col-sm-6">

                                    <button class="btn btn-success pull-right">
                                        {{ en_ar('Register Now' , 'سجل الأن') }}
                                    </button>

                                </div>

                            </div>

                        </div>

                    </form>

                </div>

            </div>

        </div>

    </div>

</div>

<a href="https://1250279.px.performadx.com/activityi;src=1250279;type=;cat=;Name=[UserName];Email=[emailid];Phone=[Phonenumber];px_lat=;px_rdid=;ord=[OrderID]" style="display: none">link</a>
<script src="https://1250279.px.performadx.com/activityi;src=1250279;type=;cat=;Name=[UserName];Email=[emailid];Phone=[Phonenumber];px_lat=;px_rdid=;ord=[OrderID]"></script>
<script src="{{ asset('js/jquery.min.js') }}"></script>

</body>

</html>