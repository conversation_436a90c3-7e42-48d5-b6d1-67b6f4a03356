<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <title>دورة لتعلم أساسيات التداول فى البورصات العالمية</title>
</head>
<body>

    <div id="app">

        <img style="width: 100%" src="{{ asset('imgs/stocks.jpeg') }}">

        <div class="container">

            <div class="row align-items-center mt-4">

                <div class="col-sm-8">

                    <h1>Application Form</h1>

                    <hr />

                    <div class="form-group">

                        <label>Name: </label>

                        <input v-validate="'required'" name="name" type="text" class="form-control" v-model="form.name">

                        <span style="color: red">@{{ errors.first('name') }}</span>

                    </div>

                    <div class="form-group">

                        <label>Age: </label>

                        <input v-validate="'required'" name="age" type="text" class="form-control" v-model="form.age">

                        <span style="color: red">@{{ errors.first('age') }}</span>

                    </div>

                    <div class="form-group">

                        <label>Phone: </label>

                        <input v-validate="'required'" name="phone" type="text" class="form-control" v-model="form.phone">

                        <span style="color: red">@{{ errors.first('phone') }}</span>

                    </div>

                    <div class="form-group">

                        <label>Address: </label>

                        <input v-validate="'required'" name="address" type="text" class="form-control" v-model="form.address">

                        <span style="color: red">@{{ errors.first('address') }}</span>

                    </div>

                    <div class="form-group">

                        <label>Education Level: </label>

                        <input v-validate="'required'" name="educationLevel" type="text" class="form-control" v-model="form.educationLevel">

                        <span style="color: red">@{{ errors.first('educationLevel') }}</span>

                    </div>

                    <div class="form-group">

                        <label>Job Title (if exists): </label>

                        <input name="jobTitle" type="text" class="form-control" v-model="form.jobTitle">

                    </div>

                    <div class="form-group">

                        <label>Why you are interested in this course: </label>

                        <textarea name="interested" class="form-control" v-model="form.interested">

                        </textarea>

                    </div>

                    <div v-if="success" class="alert alert-success" role="alert">
                        Thank you! you are registered in our course successfully!
                    </div>

                    <div v-if="failed" class="alert alert-success" role="alert">
                        Something went wrong! please check your entered data
                    </div>

                    <div class="form-group">
                        <button :disabled="loading" class="btn btn-success" @click="submit">
                            Submit
                        </button>
                    </div>

                </div>

            </div>

        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.21/dist/vue.js"></script>
    <script src="https://www.gstatic.com/firebasejs/5.7.0/firebase.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vee-validate@latest/dist/vee-validate.js"></script>

    <script>

        Vue.use(VeeValidate);

        var config = {
            apiKey: "AIzaSyA_HIQyGStTCgxlKd537PcsKtbs89GlS7k",
            authDomain: "meetup-app-b7b35.firebaseapp.com",
            databaseURL: "https://meetup-app-b7b35.firebaseio.com",
            projectId: "meetup-app-b7b35",
            storageBucket: "meetup-app-b7b35.appspot.com",
            messagingSenderId: "21277398310"
        };

        firebase.initializeApp(config);

        var db = firebase.firestore();

        db.settings({
            timestampsInSnapshots: true
        });

        

        var app = new Vue({
            el: '#app',
            data: {
                form: {
                    name: '' ,
                    age: '' ,
                    phone: '',
                    address: '',
                    educationLevel: '' ,
                    jobTitle: '' ,
                    interested: ''
                },
                loading: false,
                success: false,
                failed: false
            },
            methods: {

                submit: function () {

                    var self = this;

                    self.$validator.validate().then(function (result) {

                        if (result) {

                            self.loading = true;
                            self.success = false;
                            self.failed = false;
                            
                            var data = self.form;
                            data['date'] = new Date();

                            db.collection("users").add(data)
                            .then(function(docRef) {
                                self.loading = false;
                                self.success = true;
                                self.resetForm();
                                console.log("Document written : ", docRef);
                                self.$validator.reset();
                            })
                            .catch(function(error) {
                                self.loading = false;
                                self.failed = true;
                                self.resetForm();
                                console.error("Error adding document: ", error);
                            });
                        }else{

                            self.loading = false;

                        }

                    });

                },

                resetForm: function () {

                    this.form = {
                        name: '' ,
                        age: '' ,
                        phone: '',
                        address: '',
                        educationLevel: '' ,
                        jobTitle: '' ,
                        interested: ''
                    }

                }

            }
        });

    </script>

</body>
</html>