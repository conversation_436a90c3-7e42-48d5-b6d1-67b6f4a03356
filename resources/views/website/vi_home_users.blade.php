<!doctype html>

<html lang="en">



<head>

    <meta charset="UTF-8">

    <meta name="viewport"

          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">

    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <link href="https://fonts.googleapis.com/css?family=Tajawal" rel="stylesheet">

    <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}" />

    <link rel="stylesheet" href="http://cdn.datatables.net/1.10.16/css/jquery.dataTables.min.css">

    <title>Vi Home Users</title>

</head>



<body>



<div style="padding-top: 50px" class="container">



    <table id="example" class="table table-responsive table-bordered">



        <thead>

        <tr>

            <th>Name</th>

            <th>Email</th>

            <th>Phone</th>

        </tr>

        </thead>



        @if(count($users))



            @foreach($users as $user)



                <tr>

                    <td>{{ $user->name }}</td>

                    <td>{{ $user->email }}</td>

                    <td>{{ $user->phone }}</td>

                </tr>



            @endforeach



        @endif



    </table>



</div>



<script src="{{ asset('js/jquery.min.js') }}"></script>

<script src="http://cdn.datatables.net/1.10.16/js/jquery.dataTables.min.js"></script>

<script src="https://cdn.datatables.net/buttons/1.5.1/js/dataTables.buttons.min.js"></script>

<script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.flash.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.html5.min.js"></script>

<script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.print.min.js"></script>



<script>



    $(document).ready(function() {

        $('#example').DataTable({

            dom: 'Bfrtip',

            buttons: [

                {

                    extend: 'excel',

                    text: 'Export To Excel File' ,

                    className : 'btn btn-success'

                }

            ]

        } );

    });



</script>



</body>



</html>