<?php

function translate ($text) {

    return $text[request()->get('lang') ?? 'ar'];

}

?>

        <!doctype html>
<html lang="en" dir="{{ translate(['en' => 'ltr', 'ar' => 'rtl']) }}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">

    <title>Learn with us</title>

    <style>

        #wrapper {
            background-image: url("{{ asset('imgs/learn_design.jpg') }}");
            background-size: cover;
            background-repeat: no-repeat;
            height: 100vh;
            overflow-y: scroll;
        }

        #overlay {
            height: 100vh;
            overflow-y: scroll;
            /*background-color: rgba(0,0,0,0.3);*/
        }

        @media (max-width: 768px) {

            #wrapper {
                background-image: url("{{ asset('imgs/learn_design_mobile.jpg') }}");

            }

        }

    </style>

</head>

<body>

<div id="app">

    <div id="wrapper">

        <div id="overlay">

            <div class="container">

                <div class="row">

                    <div class="d-flex flex-column align-items-center justify-content-center py-3">

                        <div class="col-sm-8 text-center">
                            <img class="w-100" src="{{ asset('imgs/ofmlogo.png') }}">
                        </div>

                        <div class="col-sm-6 text-center">
                            <img style="width: 70%" src="{{ asset('imgs/vi_logo_10y.png') }}">
                        </div>

                    </div>

                </div>

                <div class="row">

                    <div class="col-sm-12">

                        <div id="accordion" style="width: 100%">

                            @foreach($presentations as $key => $presentation)

                                <div class="card mb-3" style="width: 100%;background-color: transparent;">

                                    <div class="card-header" style="border: 1px solid #87744e; background-color: transparent" id="heading{{ $key }}">
                                        <h5 class="mb-0">
                                            <button @click="fetchImages($event, '{{ $presentation['filename'] }}')" style="color: #87744e; font-size: 25px" class="btn btn-link w-100" data-toggle="collapse" data-target="#collapse{{ $key }}" aria-expanded="true" aria-controls="collapse{{ $key }}">
                                                {{ $presentation['title'] }}
                                            </button>
                                        </h5>
                                    </div>

                                    <div id="collapse{{ $key }}" class="collapse" aria-labelledby="heading{{ $key }}" data-parent="#accordion">
                                        <div v-if="images['{{ $presentation['filename'] }}'] || loading" class="card-body p-1" :style="{'background-color': loading ? 'white': 'darkgray'}">
                                            {{--                                        <object data="{{ asset('pdf/presentations/presentation'.($key + 1).'.pdf') }}#toolbar=0&view=FitH" type="application/pdf" style="width: 100%;height: 100vh;">--}}

                                            {{--                                            <iframe src="{{ asset('pdf/presentations/presentation'.($key + 1).'.pdf') }}#embedded=true&toolbar=0&view=FitH" frameborder="0" style="width: 100%;height: 100vh;"></iframe>--}}

                                            {{--                                        </object>--}}

                                            <div v-if="loading" class="p-5 text-center">

                                                <p class="lead">
                                                    جاري التحميل ...
                                                </p>

                                            </div>

                                            <div v-for="(img, index) in images['{{ $presentation['filename'] }}']" :key="index.toString()" class="w-100 mb-2">

                                                <img :src="img" class="w-100">

                                            </div>

                                        </div>
                                    </div>
                                </div>

                            @endforeach

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

<script src="{{ asset('js/jquery-3.4.1.slim.min.js') }}"></script>
<script src="{{ asset('js/popper.min.js') }}"></script>
<script src="{{ asset('js/bootstrap4.min.js') }}"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

<script>

    new Vue({

        el: '#app',

        data : {
            selected: '',
            images: {},
            loading: false
        },

        methods: {

            fetchImages: function (e, chapter) {

                var vm = this;

                if (vm.images[chapter]) {
                    return ;
                }

                vm.loading = true;

                axios.get('{{ route('website.learn_with_us.images') }}/' + chapter).then(function (response) {

                    vm.$set(vm.images, chapter, response.data);

                    vm.loading = false;

                });

            }

        }

    })

</script>

</body>

</html>
