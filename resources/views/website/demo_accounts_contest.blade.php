<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <title>Demo accounts contest</title>
</head>
<body>


{{--                            <h2>{{ en_ar('Leader Board', 'جدول المشاركين') }}</h2>--}}

    <div>

        <div class="leaderboard-table-container card text-dark">
            <table class="table table-striped table-bordered table-hover leaderboard-table" id="datatable_ajax">
                <thead>
                <tr role="row" class="heading">
                    <th width="10%" align="center">Position</th>
                    <th width="10%" align="center">Country</th>
                    <th width="25%" align="center">Login Id</th>
                    <th width="20%" align="left">Equity %</th>

                </tr>
                </thead>
                <tbody>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                </tbody>
            </table>
            <div id="competition-running">

            </div>
        </div>

    </div>

<script src="{{ asset('js/jquery.min.js') }}"></script>


<script type="text/javascript">

    $(document).ready(function() {

        var eid = "dmFuWU11Q0ZITEY5KzdQMS9qWnB6dz09";
        requestNewLeaderboard(eid);
        requestCompetitionRunning(eid);
        // setInterval("requestNewLeaderboard("+eid+")",15000);
        setInterval(function(){
            requestNewLeaderboard(eid);
        },5000);
        setInterval(function(){
            requestCompetitionRunning(eid);
        },1500);
        // setInterval("requestCompetitionRunning("+eid+")",1000);
    });



    function requestCompetitionRunning(eid) {
        // console.log("Updated is running");
        var leaderboard = "https://competition.onefinancialmarkets.com/compete/json_other?callback=?";

        $.getJSON(leaderboard,'id='+eid+'&action=competition-running',function(res){
            $("#competition-running").html(res.message);
            console.log(res);
        });
    }

    function requestNewLeaderboard(eid) {
        console.log("Updated leaderboard");
        $(".leaderboard-table tbody td").remove();
        var leaderboard = "https://competition.onefinancialmarkets.com/compete/json_leaderboard?callback=?";
        $.getJSON(leaderboard,'id='+eid,function(res){

            console.log(res);

            $(".leaderboard-table tbody td").remove();

            $.each(res, function(idx, obj) {
                flag = "";
                if(obj.country_flag!=null) {
                    flag="<img src='https://competition.onefinancialmarkets.com/assets/img/flags/24/"+obj.country_flag+".png' alt='"+obj.country+"' />";
                }
                var h = "<tr><td align='center'><div class='position pos"+obj.position+"'>"+obj.position+"</div></td><td align='center'>"+flag+"</td><td align='center'>"+ obj.login_id +"</td><td align='center'>"+obj.equity_percentage+"</td></tr>";
                // h += "<td align='center'>"+flag+"</td>";
                // h += "<td align='center'>"+obj.login_id+"</td>";
                // h += "<td align='center'>"+obj.equity_percentage+"</td>";
                // h += "</tr>";
                // console.log(obj);
                $(".leaderboard-table tbody").append(h);
            });
        });
    }

</script>

</body>
</html>
