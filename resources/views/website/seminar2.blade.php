@extends('layouts.master')

@section('title' , $seminar->title)

@section('content')

    <div style="margin-top: 60px" class="home">

        <div style="margin-bottom: 10px" class="container banner text-center" id="main-page">

            <div class="top-content">

                <div class="container">

                    @include('partials.success')
                    @include('partials.failed')
                    @include('partials.validation_errors')

                    <img @if($seminar->isLogo('yasmeen')) style="width: 50%" @endif src="{{ $seminar->logoUrl }}">

                    @if($seminar->isLogo('vimarkets'))

                        <h3>بالتعاون مع</h3>

                        <img src="{{ asset('imgs/ofm-logo-295x62.png') }}">

                    @endif

                    <h3 class="seminar-title">
                        {{ $seminar->title }}
                    </h3>

                    <h3 class="seminar-title">
                        {{ $seminar->place }}
                    </h3>

                </div>

            </div>

        </div>

        <div>

            <div class="container text-center">

                <div class="row">

                    <img style="width: 100%" src="{{ asset('imgs/design.jpg') }}">

                </div>

            </div>

        </div>

        <div style="margin-bottom: 20px" class="container">

            <div class="row">

                <div style="padding:20px 0;color:#FFFFFF;background-image: url('{{ asset('imgs/bg_eventcamp.jpg') }}')" class="bottom-content text-right">

                    <div class="container">

                        <div class="row">

                            <div class="col-md-3">

                                @if ($seminar->id == 30)
                                    <h3 class="title">
                                        موعد ومكان المسابقة
                                    </h3>
                                @else

                                    <h3 class="title">موعد و مكان المحاضرة</h3>

                                @endif

                            </div>

                            <div class="col-md-3">
                                <h4>
                                    <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                    التاريخ
                                    <br>
                                    {{ $seminar->date }}
                                </h4>
                            </div>

                            <div class="col-md-3">
                                <h4>
                                    <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                    الوقت
                                    <br>
                                    {{ $seminar->time }}
                                </h4>
                            </div>

                            <div class="col-md-3">
                                <h4>
                                    <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                    المكان
                                    <br>
                                    {{ $seminar->place }}
                                </h4>
                            </div>

                        </div>

                    </div>

                </div>

            </div>

        </div>

        @if(count($seminar->contents))

            <div style="margin-bottom: 20px">

                <div class="container">

                    <div class="row">

                        <h2>
                            <strong>
                                بحضورك لهذه الندوة ستتعلم
                            </strong>
                        </h2>

                        <ol>

                            @foreach($seminar->contents as $content)

                                <li>
                                    <strong>
                                        {{ $content }}
                                    </strong>
                                </li>

                            @endforeach

                        </ol>

                    </div>

                </div>

            </div>

        @endif

        <div class="register" id="register">

            <div class="container">

                <div class="row">

                    <div class="form-container">

                        @include('partials.validation_errors')

                        {{ Form::open(['route' => 'participant_register']) }}

                        {{ Form::hidden('seminar_id' , $seminar->id) }}

                        @if($seminar->id == 25)

                            <input type="hidden" name="country" value="Egypt" />

                        @else

                            <div class="form-group">
                                <h3>{{ Form::label('country' , '* البلد : ') }}</h3>
                                {{ Form::select('country' , $countries , null , ['class' => 'form-control' , 'id' => 'country' , 'required' => true]) }}
                            </div>

                        @endif

                        <div class="form-group">
                            <h3>{{ Form::label('first_name' , '* الأسم الاول : ') }}</h3>
                            {{ Form::text('first_name' , null , ['class' => 'form-control' , 'id' => 'first_name' , 'required' => true]) }}
                        </div>

                        <div class="form-group">
                            <h3>{{ Form::label('last_name' , '* أسم العائلة : ') }}</h3>
                            {{ Form::text('last_name' , null , ['class' => 'form-control' , 'id' => 'last_name' , 'required' => true]) }}
                        </div>

                        <div class="form-group">
                            <h3>{{ Form::label('email' , '* البريد الألكترونى : ') }}</h3>
                            {{ Form::email('email' , null , ['class' => 'form-control' , 'id' => 'email' , 'required' => true]) }}
                        </div>


                        <div class="form-group">
                            <h3>{{ Form::label('phone' , '* الهاتف : ') }}</h3>
                            {{ Form::number('phone' , null , ['class' => 'form-control' , 'id' => 'phone' , 'required' => true]) }}
                        </div>

                        <div class="form-group">
                            {{ Form::submit('سجل'  , ['class' => 'btn btn-success form-control']) }}
                        </div>

                        {{ Form::close() }}

                    </div>

                </div>

            </div>

        </div>

        @if($seminar->map_address)

            <div class="map" id="map">

                <div class="container">

                    <h2 class="title">
                        الخريطة
                    </h2>

                    <iframe
                            width="100%"
                            height="450"
                            frameborder="0" style="border:0"
                            src="https://www.google.com/maps/embed/v1/place?key=AIzaSyCz70rhssH_nkZqe1Q6QrV7CdiJaxjUUMw
                &q={{ $seminar->map_address }}" allowfullscreen>
                    </iframe>

                </div>

            </div>

        @endif

        <div class="footer">

            <div class="container">

                <div class="social">


                </div>

                81٪ من حسابات المستثمرين الأفراد يخسرون عند تداول العقود مقابل الفروقات مع هذه الأداة. يجب أن تفكر فيما إذا كنت تفهم كيف تعمل العقود مقابل الفروقات وفيما إذا كنت تستطيع تحمل مخاطر عالية تسبب فقدان أموالك.

            </div>

        </div>

    </div>

@endsection