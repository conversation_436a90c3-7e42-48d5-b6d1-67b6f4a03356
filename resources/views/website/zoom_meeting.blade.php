<?php

function translate ($text) {

    return $text[request()->get('lang') ?? 'en'];

}

?>

        <!doctype html>
<html lang="en" dir="{{ translate(['en' => 'ltr', 'ar' => 'rtl']) }}">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">

    <title>Mr <PERSON></title>

    <style>

        body {
            color: white;
        }

        input.form-control {
            background-color: transparent;
            color: white;
        }

        #container {
            background-image: url("{{ asset('imgs/mr_talal.png') }}");
            height: 100vh;
            background-size: cover;
            overflow: scroll;
        }

        #overlay {
            background-color: rgba(0,0,0,0.1);
            height: 100vh;
            overflow: scroll;
        }

    </style>

</head>

<body>

<div id="container">

    <div id="overlay">

        <div class="container">

            <div class="row vh-100 align-items-center justify-content-end">

                <div class="col-12 col-sm-12 col-md-6">

                    <div class="p-3">

                        <div class="d-flex align-items-center justify-content-center">

                            <img style="width: 150px" src="{{ asset('imgs/talal_logo.png') }}" alt="">
                            
                        </div>

                        <div class="d-flex align-items-center justify-content-center">

                            <img style="width: 250px" src="{{ asset('imgs/slogan.png') }}" alt="">

                        </div>

                        @include('partials.success')
                        @include('partials.failed')

                        <form method="post" action="{{ route('zoom_meeting_post') }}">

                            {{ csrf_field() }}

                            <div class="form-group">

                                <label for="first_name">First Name</label>

                                <input class="form-control" name="first_name" value="{{ old('first_name') }}" required>

                            </div>

                            <div class="form-group">

                                <label for="first_name">Last Name</label>

                                <input class="form-control" name="last_name" value="{{ old('last_name') }}" required>

                            </div>

                            <div class="form-group">

                                <label for="first_name">Phone</label>

                                <input class="form-control" name="phone" value="{{ old('phone') }}" required>

                            </div>

                            <div class="form-group">

                                <label for="first_name">Email</label>

                                <input class="form-control" name="email" value="{{ old('email') }}" required>

                            </div>

                            <div class="form-group">

                                <button type="submit" class="btn btn-success btn-block">

                                    Send

                                </button>

                            </div>

                        </form>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

<script src="{{ asset('js/jquery-3.4.1.slim.min.js') }}"></script>
<script src="{{ asset('js/popper.min.js') }}"></script>
<script src="{{ asset('js/bootstrap4.min.js') }}"></script>

<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

</body>

</html>
