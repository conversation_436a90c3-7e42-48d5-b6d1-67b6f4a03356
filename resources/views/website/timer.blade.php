<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport"
              content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <link rel="stylesheet" href="{{ asset('css/bootstrap.css') }}" />
        {{--<link rel="stylesheet" href="{{ asset('css/bootstrap-rtl.css') }}" />--}}
        <link rel="stylesheet" href="{{ asset('css/TimeCircles.css') }}">
        <title>Timer</title>
    </head>

    <style>

        @font-face {
            font-family: 'digital-clock';
            src : url('{{ asset('fonts/digital-7.ttf') }}')
        }

        .app-container {
            background-image: url("{{ asset('imgs/competition_background.jpg') }}");
            
            background-size: cover;
        }

    </style>

    <body>

        <div class="app-container">

            <div class="container">

                <div class="row">

                    <div class="col-sm-8 col-sm-offset-2">

                        <div style="padding-top: 20px" class="text-center">

                            <img style="width: 50%" src="{{ asset('storage/7bars/vi-markets-logo.png') }}">
                            
                            <div style="height : 20px"></div>
                            
                            <img style="width: 50%" src="{{ asset('storage/7bars/one-logo.png') }}">

                            <h1 style="color: #ffffff">
                                30 MINUTES TRADER
                            </h1>

                        </div>

                        {{--<div class="example" data-timer="1801"></div>--}}

                        <div style="padding: 10px 0 10px 0" class="text-center">
                            <div style="color: white" id="countdownExample">
                                <div style="font-size: 160px;font-family: 'digital-clock'" class="values">
                                    00:30:00
                                </div>
                            </div>
                        </div>

                        <div style="color: white" class="text-center">

                            <h1>
                                WIN
                                <span style="color: #f0f000">1000$*</span>
                                trading credit
                            </h1>

                            <p>
                                CFDs and Fx are high risk leveraged products <br>
                                *Terms & conditions apply
                            </p>
                            
                            <p style="direction: rtl">
    التداول بالعقود مقابل الفورقات والعملات العالمية بنظام الهامش بحمل مخاطر
                                <br />
                                *تطبق الشروط والاحكام

                            </p>

                        </div>

                    </div>

                </div>

            </div>



            <div class="text-center btns-container">

                <button class="btn btn-success btn-lg start">Start</button>
                {{--<button class="btn btn-danger btn-lg stop">Stop</button>--}}
                {{--<button class="btn btn-info btn-lg restart">Restart</button>--}}

            </div>

        </div>

        <script src="{{ asset('js/jquery.min.js') }}"></script>
        <script src="{{ asset('js/TimeCircles.js') }}"></script>
        <script src="{{ asset('js/easytimer.min.js') }}"></script>

        <script>

            $(".example").TimeCircles({
                circle_bg_color: "#2f3354" ,
                start: false ,
                use_background : true ,
                count_past_zero: false ,
                direction: "Counter-clockwise" ,
                time : {
                    Days : { show : false } ,
                    Hours : { show : false } ,
                    Minutes: { color: "#8284ca" },
                    Seconds: { color: "#8284ca" }
                }
            });

            var timer = new Timer();

            // timer.addEventListener('targetAchieved', function (e) {
            //     $('#countdownExample .values').html('KABOOM!!');
            // });

            // $('#countdownExample .values').html(timer.getTimeValues().toString());

            $(".start").click(function(){

                timer.start({countdown: true, startValues: {seconds: 1800}});

                timer.addEventListener('secondsUpdated', function (e) {
                    $('#countdownExample .values').html(timer.getTimeValues().toString());
                });

                // $(".example").TimeCircles().start();

            });

            $(".stop").click(function(){

                // $(".example").TimeCircles().stop();

            });

            $(".restart").click(function(){

                // $(".example").TimeCircles().restart();

            });




        </script>

    </body>

</html>
