@php

    $lang = request()->route()->parameter('lang');

    function ar_en ($ar, $en) {

        $lang = request()->route()->parameter('lang');

        return $lang == 'ar' ? $ar : $en;

    }

@endphp

        <!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <title>Kafo Program</title>
    <style>

        .background {
            background-image: url("{{ asset('imgs/mousa2.jpg') }}");
            background-size: cover;
            background-repeat: no-repeat;
            margin-top: -100px;
        }

        .logo {
            width: 400px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .background {
                margin-top: -140px;
            }
            .logo {
                width: 300px;
            }
        }

        .form-card {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 20px;
            width: 100%;
            /*margin-top: 50px;*/
        }

        .content {
            /*margin-top: 50px;*/
            padding: 20px;
            width: 100%;
        }

        .content p {
            font-size: 16px;
            color: #ffffff;
        }

    </style>
</head>
<body>

<div id="app">

    <div class="background" id="background">

        <div class="container">

            <div class="row justify-content-center align-items-center"
                 style="padding-top: 120px">

                {{--<div class="col-md-4 d-flex justify-content-center">--}}

                    {{--<div class="py-2 pt-4">--}}
                        {{--<img src="{{ asset('imgs/vimarkets_logo.png') }}">--}}
                    {{--</div>--}}

                {{--</div>--}}

                <div class="col-md-4 d-flex justify-content-center">

                    <div class="py-3">
                        <img class="logo" src="{{ asset('imgs/kafu_logo.png') }}">
                    </div>

                </div>

                {{--<div class="col-md-4 d-flex justify-content-center">--}}

                    {{--<div class="py-3">--}}
                        {{--<img src="{{ asset('imgs/ofmlogo.png') }}" style="width: 300px;">--}}
                    {{--</div>--}}

                {{--</div>--}}

            </div>

            <div class="row justify-content-center py-3">

                <div>
                    @if($lang != 'ar')
                        <a class="btn btn-primary" href="{{ route('website.kafo.index', 'ar') }}">
                            اللغة العربية
                        </a>
                    @else
                        <a class="btn btn-primary" href="{{ route('website.kafo.index', 'en') }}">
                            English
                        </a>
                    @endif
                </div>

            </div>

            <div class="row">

                <div class="col-md-6">

                    <div class="d-flex justify-content-start">

                        <div class="form-card">

                            @include('partials.success')
                            @include('partials.validation_errors')
                            @include('partials.failed')

                            <form method="post" action="{{ route('website.kafo.register') }}" enctype="multipart/form-data">

                                {{ csrf_field() }}

                                <div class="form-group">

                                    <label>
                                        {{ ar_en('الأسم', 'Name')  }}
                                    </label>

                                    <input type="text" name="name" class="form-control">

                                </div>

                                <div class="form-group">

                                    <label>
                                        {{ ar_en('الهاتف', 'Phone') }}
                                    </label>

                                    <input type="text" name="phone" class="form-control">

                                </div>

                                <div class="form-group">

                                    <label>
                                        {{ ar_en('البريد الألكترونى', 'Email') }}
                                    </label>

                                    <input type="email" name="email" class="form-control">

                                </div>

                                <div class="form-group">

                                    <label>CV</label>

                                    <br >

                                    <input type="file" name="cv">

                                </div>

                                <div class="form-group">

                                    <button type="submit" class="btn btn-success">
                                        {{ ar_en('أرسال', 'Send') }}
                                    </button>

                                </div>

                            </form>

                        </div>

                    </div>

                </div>

                <div class="col-md-6">

                    <div class="d-flex justify-content-end">

                        @if($lang == 'ar')
                            <div class="content" style="direction: rtl;text-align: right">
                                @else
                                    <div class="content">
                                        @endif

                                        <p>
                                            {{
                                                ar_en(
                                                "برنامج كفو هو برنامج مخصص لطلبة الجامعات والمعاهد الكويتيين وفق اختصاصاتهم المختلفة لمنحهم فرص وظيفية وتدريبية اثناء دراستهم لاكتساب الخبرة في مختلف المجالات وتنمية وتطوير مهاراتهم بشكل خاص تحت اشراف موجهين من الشركة ضمن دوام جزئي.",
                                                "Kafu program dedicated only for Kuwaiti students in universities and institutes, according to their different specialties, to provide them career opportunities and training during their studies to gain experience in various fields and develop their skills in particular, under the supervision of the company's mentors, within a part-time job. "
                                                )
                                            }}
                                        </p>

                                        <p>
                                            {{
                                                ar_en(
                                                "فالهدف من البرنامج هو تقديم تجربة نوعية للطلبة واعدادهم للدخول في سوق العمل من خلال الفعاليات والمشاريع والمناقشات والافكار الابداعية التي يمكنهم طرحها على ارض الواقع وبذلك سوف يحدثون تأثير ايجابي بالاضافة الى اكتسابهم للخبرات العملية والقدرة على مواجهة تحديات سوق العمل المختلفة.",
                                                "The aim of the program is to provide a quality experience for students and prepare them to enter the labor market through events, projects, discussions and creative ideas that they can put on the ground. This will have a positive impact as well as gaining practical experience and ability to meet the challenges of the labor market."
                                                )
                                            }}
                                        </p>

                                        <p>
                                            {{
                                                ar_en(
                                                    "اعلان توظيف لطلاب الجامعات و المعاهد الكويتيون الراغبون بالحصول على الخبرة العملية خلال الدراسة الجامعية، ارسال السيرة الذاتية الى البريد الالكتروني <EMAIL> او ارفاقه في هذا الرابط",
                                                    "Employment announcement for college & university Kuwaiti students who are looking to gain this experience, please send your CV to below email address:
            <EMAIL> Or attach it to this link. "
                                                )
                                            }}
                                        </p>

                                        <p>
                                            {{
                                                ar_en(
                                                    "ملاحظة: سيحصل الطلاب على راتب شهري بالاضافة الى شهادة خبرة لاستخدامها في برنامج التدريب العملي للطلاب او في التقديم على وظائف عمل مستقبلية. هناك فرصة للطلبة المميزين بالحصول على عرض عمل بدوام كامل بعد الانتهاء من الدراسة و الحصول على الشهادة الجامعية.",
                                                    "Note: selected students will be paid a fair monthly wage as well as receive an experience certificate for internship program or future job applications. Top performance may be offered a full-time job offer after receiving the education certificate."
                                                )
                                            }}
                                        </p>

                                    </div>

                            </div>

                    </div>

                </div>

                <div class="row justify-content-center">

                    <p style="color: #ffffff;text-align: center">

                        {{
                            ar_en(
                                "* 78٪؜ من حسابات المستثمرين الأفراد يخسرون عند تداول العقود مقابل الفروقات مع هذه الأداة. يجب أن تفكر فيما إذا كنت تفهم كيف تعمل العقود مقابل الفروقات وفيما إذا كنت تستطيع تحمل مخاطر عالية تسبب فقدان أموالك.",
                                "*78% of retail investor accounts lose money when trading CFDs with this provider. You should consider whether you understand how CFDs work and whether you can afford to take the high risk of losing your money."
                            )
                        }}

                    </p>

                </div>

            </div>

        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.5.21/dist/vue.js"></script>
    <script src="https://www.gstatic.com/firebasejs/5.7.0/firebase.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vee-validate@latest/dist/vee-validate.js"></script>

{{--<script>--}}

{{--$(document).ready(function () {--}}

{{--// var background = document.getElementById('background');--}}
{{--//--}}
{{--// background.style.width = window.outerWidth + 'px';--}}
{{--// background.style.height = window.outerHeight + 'px';--}}

{{--});--}}

{{--Vue.use(VeeValidate);--}}

{{--var config = {--}}
{{--apiKey: "AIzaSyCTJ1yLg0ewwqtDl1MiXTxP7hy5ILhpv-w",--}}
{{--authDomain: "kafo-be4af.firebaseapp.com",--}}
{{--databaseURL: "https://kafo-be4af.firebaseio.com",--}}
{{--projectId: "kafo-be4af",--}}
{{--storageBucket: "kafo-be4af.appspot.com",--}}
{{--messagingSenderId: "1007155827005"--}}
{{--};--}}

{{--firebase.initializeApp(config);--}}

{{--var db = firebase.firestore();--}}

{{--db.settings({--}}
{{--timestampsInSnapshots: true--}}
{{--});--}}

{{--var app = new Vue({--}}
{{--el: '#app',--}}
{{--data: {--}}
{{--form: {--}}
{{--name: '' ,--}}
{{--age: '' ,--}}
{{--phone: '',--}}
{{--address: '',--}}
{{--educationLevel: '' ,--}}
{{--jobTitle: '' ,--}}
{{--interested: ''--}}
{{--},--}}
{{--loading: false,--}}
{{--success: false,--}}
{{--failed: false--}}
{{--},--}}
{{--methods: {--}}

{{--submit: function () {--}}

{{--var self = this;--}}

{{--self.$validator.validate().then(function (result) {--}}

{{--if (result) {--}}

{{--self.loading = true;--}}
{{--self.success = false;--}}
{{--self.failed = false;--}}

{{--db.collection("users").add(self.form)--}}
{{--.then(function(docRef) {--}}
{{--self.loading = false;--}}
{{--self.success = true;--}}
{{--self.resetForm();--}}
{{--console.log("Document written : ", docRef);--}}
{{--self.$validator.reset();--}}
{{--})--}}
{{--.catch(function(error) {--}}
{{--self.loading = false;--}}
{{--self.failed = true;--}}
{{--self.resetForm();--}}
{{--console.error("Error adding document: ", error);--}}
{{--});--}}
{{--}else{--}}

{{--self.loading = false;--}}

{{--}--}}

{{--});--}}

{{--},--}}

{{--resetForm: function () {--}}

{{--this.form = {--}}
{{--name: '' ,--}}
{{--age: '' ,--}}
{{--phone: '',--}}
{{--address: '',--}}
{{--educationLevel: '' ,--}}
{{--jobTitle: '' ,--}}
{{--interested: ''--}}
{{--}--}}

{{--}--}}

{{--}--}}
{{--});--}}

{{--</script>--}}

</body>
</html>