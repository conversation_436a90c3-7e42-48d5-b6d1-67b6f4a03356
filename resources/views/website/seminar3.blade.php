@extends('layouts.master')

@section('title' , $seminar->title)

@section('content')

    <div class="home">

        {{--professional-presentation-background-for-office.jpg--}}

        <div style="background-image: url('{{ asset('imgs/app-background.jpg') }}')">

            <div class="banner text-center" id="main-page">

                <div class="top-content">

                    @include('partials.success')
                    @include('partials.failed')
                    @include('partials.validation_errors')

                    <div class="text-center" style="margin-bottom: 20px">
                        <h1 class="seminar-title">
                            {{ $seminar->title }}
                        </h1>
                    </div>

                    <img @if($seminar->isLogo('yasmeen')) style="width: 50%" @endif src="{{ $seminar->logoUrl }}">

                    @if($seminar->isLogo('vimarkets'))

                        <h3>بالتعاون مع</h3>

                        <img src="{{ asset('imgs/ofm-logo-295x62.png') }}">

                    @endif

                    {{--<h3>--}}
                    {{--{{ $seminar->place }}--}}
                    {{--</h3>--}}

                </div>

            </div>

            <div>

                <div style="padding:20px 0;color:#FFFFFF" class="bottom-content text-right">

                    <div class="container">

                        <div class="row">

                            {{--<div class="col-md-3">--}}

                            {{--@if ($seminar->id == 30)--}}
                            {{--<h3 class="title">--}}
                            {{--موعد ومكان المسابقة--}}
                            {{--</h3>--}}
                            {{--@else--}}

                            {{--<h3 class="title">موعد و مكان المحاضرة</h3>--}}

                            {{--@endif--}}

                            {{--</div>--}}

                            <div class="col-md-4 text-center">
                                <h3>
                                    <p>
                                        <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                        التاريخ
                                    </p>

                                    {{ $seminar->date }}
                                </h3>
                            </div>

                            <div class="col-md-4 text-center">
                                <h3>
                                    <p>
                                        <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                        الوقت
                                    </p>

                                    {{ $seminar->time }}
                                </h3>
                            </div>

                            <div class="col-md-4 text-center">
                                <h3>
                                    <p>
                                        <i class="fa fa-calendar-o" aria-hidden="true"></i>
                                        المكان
                                    </p>
                                    {{ $seminar->place }}
                                </h3>
                            </div>

                        </div>

                    </div>

                </div>

            </div>

            <div class="container">
                <hr>
            </div>

            <div class="text-center" style="color: #FFFFFF">
                <div class="text-center" style="margin-bottom: 20px">
                    <h3 class="seminar-title" style="padding: 10px; margin: 0">
                        محتويات الندوة
                    </h3>
                </div>

                <div>
                    @if(count($seminar->contents))

                        <ul style="display: inline-block" class="list-unstyled">

                            @foreach($seminar->contents as $content)

                                <li>
                                    <p class="lead">
                                        {{ $content }}
                                    </p>
                                </li>

                            @endforeach

                        </ul>

                    @endif
                </div>
            </div>

            <div class="container">
                <hr>
            </div>

            <div style="color: #FFFFFF">

                {{ Form::open(['route' => 'participant_register']) }}

                {{ Form::hidden('seminar_id' , $seminar->id) }}

                <div class="container">

                    <div class="row">

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('country' , '* البلد : ') }}</h4>
                                {{ Form::select('country' , $countries , null , ['class' => 'form-control' , 'id' => 'country' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('first_name' , '* الأسم الاول : ') }}</h4>
                                {{ Form::text('first_name' , null , ['class' => 'form-control' , 'id' => 'first_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('last_name' , '* أسم العائلة : ') }}</h4>
                                {{ Form::text('last_name' , null , ['class' => 'form-control' , 'id' => 'last_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('email' , '* البريد الألكترونى : ') }}</h4>
                                {{ Form::email('email' , null , ['class' => 'form-control' , 'id' => 'email' , 'required' => true]) }}
                            </div>
                        </div>

                    </div>

                    <div class="row" style="display: flex;align-items: flex-end">

                        <div class="col-xs-6 col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('phone' , '* الهاتف : ') }}</h4>
                                {{ Form::number('phone' , null , ['class' => 'form-control' , 'id' => 'phone' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-xs-6 col-md-3">
                            <div class="form-group">
                                {{ Form::submit('سجل'  , ['class' => 'btn btn-success form-control']) }}
                            </div>
                        </div>

                    </div>

                </div>

                {{ Form::close() }}

            </div>

        </div>

        <div>

            <div class="container text-center">

                <div class="row">

                    {{--<img style="width: 100%" src="{{ asset('imgs/design.jpg') }}">--}}

                </div>

            </div>

        </div>

        @if($seminar->map_address)

            <div class="map" id="map" style="margin: 10px 0;">

                <div class="container">
                    <iframe
                            width="100%"
                            height="450"
                            frameborder="0" style="border:0"
                            src="https://www.google.com/maps/embed/v1/place?key=AIzaSyCz70rhssH_nkZqe1Q6QrV7CdiJaxjUUMw
                &q={{ $seminar->map_address }}" allowfullscreen>
                    </iframe>

                </div>

            </div>

        @endif

        <div class="footer">

            <div class="container">

                <div class="social">


                </div>

                81٪ من حسابات المستثمرين الأفراد يخسرون عند تداول العقود مقابل الفروقات مع هذه الأداة. يجب أن تفكر فيما إذا كنت تفهم كيف تعمل العقود مقابل الفروقات وفيما إذا كنت تستطيع تحمل مخاطر عالية تسبب فقدان أموالك.

            </div>

        </div>

    </div>

@endsection