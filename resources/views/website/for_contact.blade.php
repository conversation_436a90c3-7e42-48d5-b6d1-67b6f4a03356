@extends('layouts.master')

@section('title' , $seminar->title)

@section('content')

    <div>

        {{--title--}}

        <div class="container">

            <div class="row">

                @if( session()->has('success') )

                    <div class="alert alert-success" role="alert">

                        <h2>
                            تم الأرسال بنجاح
                        </h2>

                    </div>

                @endif

                @include('partials.failed')
                @include('partials.validation_errors')

                {{--<div style="margin: 40px 0" class="text-center">--}}

                    {{--<h1>{{ $seminar->title }}</h1>--}}

                {{--</div>--}}

            </div>

        </div>

        {{--desgin--}}

        <div>

            <img style="width: 100%" src="{{ asset('imgs') . '/' . $logo }}" />

        </div>

        {{--register--}}

        <div style="background-color: #EEEEEE;margin-bottom: 20px">

            <div>

                {{ Form::open(['route' => 'participant_register']) }}

                {{ Form::hidden('seminar_id' , $seminar->id) }}

                <div class="container">

                    <div class="row">

                        <div style="border-bottom: 1px solid #000000" class="page-header">
                            <h2>
                                للتواصل
                            </h2>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('country' , 'الدولة : ') }}</h4>
                                <select name="country" class="form-control">
                                    {{--<option value="الكويت">الكويت</option>--}}

                                    {{--<option value="الامارات">الامارات</option>--}}
                                    {{--<option value="عمان">عمان</option>--}}
                                    {{--<option value="الكويت">الكويت</option>--}}
                                    {{--<option value="الأردن">الأردن</option>--}}
                                    {{--<option value="السعودية">السعودية</option>--}}
                                    {{--<option value="البحرين">البحرين</option>--}}
                                    {{--<option value="مصر">مصر</option>--}}
                                    {{--<option value="قطر">قطر</option>--}}
                                    @foreach($countries as $country)
                                        <option value="{{ $country }}">{{ $country }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('first_name' , '* الأسم الاول : ') }}</h4>
                                {{ Form::text('first_name' , null , ['class' => 'form-control' , 'id' => 'first_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('last_name' , '* أسم العائلة : ') }}</h4>
                                {{ Form::text('last_name' , null , ['class' => 'form-control' , 'id' => 'last_name' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-sm-3">
                            <div class="form-group">
                                <h4>{{ Form::label('email' , '* البريد الألكترونى : ') }}</h4>
                                {{ Form::email('email' , null , ['class' => 'form-control' , 'id' => 'email' , 'required' => true]) }}
                            </div>
                        </div>

                    </div>

                    <div class="row" style="display: flex;align-items: flex-end">

                        <div class="col-xs-6 col-md-3">
                            <div class="form-group">
                                <h4>{{ Form::label('phone' , '* الهاتف : ') }}</h4>
                                {{ Form::number('phone' , null , ['class' => 'form-control' , 'id' => 'phone' , 'required' => true]) }}
                            </div>
                        </div>

                        <div class="col-xs-6 col-md-3">
                            <div class="form-group">
                                {{ Form::submit('سجل'  , ['class' => 'btn btn-success form-control']) }}
                            </div>
                        </div>

                    </div>

                </div>

                {{ Form::close() }}

            </div>

        </div>

        {{--footer--}}

        <div style="background-color: black">

            <div class="container">

                <div class="row">

                    <div class="col-sm-12">

                        <p style="color: #FFFFFF;padding: 50px 0 30px 0;" class="lead">

                            78٪ من حسابات المستثمرين الأفراد يخسرون عند تداول العقود مقابل الفروقات مع هذه الأداة. يجب أن تفكر فيما إذا كنت تفهم كيف تعمل العقود مقابل الفروقات وفيما إذا كنت تستطيع تحمل مخاطر عالية تسبب فقدان أموالك.

                        </p>

                    </div>

                </div>

            </div>

        </div>

    </div>

@endsection