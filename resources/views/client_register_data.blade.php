<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">

    <title>VI Markets Registrations {{ $register_type == \App\Models\Client::REGISTER_TYPE_CLIENT ? 'Clients' : 'Employees' }}</title>

</head>

<body>

<div id="container">

    <div class="row">

        <div class="col-sm-12">
            <h1>
                {{ $register_type == \App\Models\Client::REGISTER_TYPE_CLIENT ? 'Clients' : 'Employees' }}
            </h1>
        </div>

        <div class="col-sm-12">

            <table class="table table-bordered table-responsive-sm table-striped">

                <tr>
                    <th>
                        First Name
                    </th>
                    <th>
                        Last Name
                    </th>
                    <th>
                        Phone Number
                    </th>
                    <th>
                        Email
                    </th>
                    <th>
                        Work Place
                    </th>
                    <th>
                        Job Title
                    </th>
                    <th>
                        Years of work
                    </th>
                    <th>
                        Deposit Amount
                    </th>
                    <th>
                        Work sector
                    </th>
                    @if($register_type == \App\Models\Client::REGISTER_TYPE_EMPLOYEE)
                        <th>
                            Opened by
                        </th>
                        <th>
                            Marketed by
                        </th>
                    @endif
                    @foreach(\App\Models\Client::$files as $file)
                        <th>
                            {{ $file }}
                        </th>
                    @endforeach
                </tr>

                @foreach($registrations as $registration)

                    <tr>
                        <td>
                            {{ $registration->first_name }}
                        </td>
                        <td>
                            {{ $registration->last_name }}
                        </td>
                        <td>
                            {{ $registration->phone_number }}
                        </td>
                        <td>
                            {{ $registration->email }}
                        </td>
                        <td>
                            {{ $registration->work_place }}
                        </td>
                        <td>
                            {{ $registration->job_title }}
                        </td>
                        <td>
                            {{ $registration->years_of_work }}
                        </td>
                        <td>
                            {{ $registration->deposit_amount }}
                        </td>
                        <td>
                            {{ $registration->section }}
                        </td>
                        @if($register_type == \App\Models\Client::REGISTER_TYPE_EMPLOYEE)
                            <td>
                                {{ $registration->opened_by }}
                            </td>
                            <td>
                                {{ $registration->marketed_by }}
                            </td>
                        @endif
                        @foreach(\App\Models\Client::$files as $file)
                            <td>
                                @if($registration->{$file})
                                    @foreach($paths = explode('separator', $registration->{$file}) as $key => $path)
                                        <div>
                                            <a href="{{ route('employee_client_register.download', [$registration->id,  encrypt($path)]) }}">
                                                Download{{ $key + 1 }}
                                            </a>
                                        </div>
                                    @endforeach
                                @endif
                            </td>
                        @endforeach
                    </tr>

                @endforeach

            </table>

            <div class="text-center">

                {{ $registrations->links() }}

            </div>

        </div>

    </div>

</div>

<script src="{{ asset('js/jquery-3.4.1.slim.min.js') }}"></script>
<script src="{{ asset('js/popper.min.js') }}"></script>
<script src="{{ asset('js/bootstrap4.min.js') }}"></script>

<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

</body>

</html>
