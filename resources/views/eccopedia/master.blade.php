<!doctype html>
<html lang="en" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
{{--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css" integrity="sha384-zCbKRCUGaJDkqS1kPbPd7TveP5iyJE0EjAuZQTgFLD2ylzuqKfdKlfG/eSrtxUkn" crossorigin="anonymous">    <style href="{{ asset('eccopedia/css/bootstrap.min.rtl.css') }}"></style>--}}
    <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.2.1/css/bootstrap.min.css" integrity="sha384-vus3nQHTD+5mpDiZ4rkEPlnkcyTP+49BhJ4wJeJunw06ZAp+wzzeBPUXr42fi8If" crossorigin="anonymous">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css" integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous"/>
    <style>

        @font-face {
            src: url("{{ asset('eccopedia/assets/fonts/alfont_com_AlFont_com_SST-Arabic-Medium.ttf') }}");
            font-family: sst;
        }

        body, * {
            font-family: sst; !important;
        }

        .navbar .navbar-nav a {
            font-size: 20px;
        }

        .navbar .navbar-nav li {
            margin-right: 5px;
            margin-left: 5px;
        }

        .purple {
            color: #9d3e98;
        }

        .bg-purple {
            background-color: #9d3e98;
        }

        .navbar .dropdown-menu div[class*="col"] {
            margin-bottom: 1rem
        }

        .navbar .dropdown-menu {
            border: none;
            background-color: rgb(0, 115, 168) !important;
            margin-top: 8px
        }

        .navbar {
            padding-top: 0px;
            padding-bottom: 0px
        }

        .navbar .nav-item {
            padding: .5rem .5rem;
            margin: 0 .25rem
        }

        .navbar .dropdown {
            position: static
        }

        .navbar .dropdown-menu {
            width: 100%;
            left: 0;
            right: 0;
            top: 45px
        }

        .navbar .dropdown:hover .dropdown-menu,
        .navbar .dropdown .dropdown-menu:hover {
            display: block !important
        }

        .navbar .dropdown-menu {
            border: 1px solid rgba(0, 0, 0, .15);
            background-color: black; !important;
            top: 54px;
        }

        a.i {
            margin-top: -20px;
            font-weight: 400;
            color: white
        }

        a.catogary {
            margin-left: -20px;
            color: white
        }

        button.srch {
            padding: 0px
        }

        @media only screen and (max-width: 1024px) and (min-width: 768px) {
            input.srch {
                padding: 0px;
                width: 100%
            }
        }

        a.nav-link.active {
            font-weight: 700;
            color: white;
            background-color: black;
        }

        a.navbar-brand {
            color: white
        }

        span.navbar-toggler.icon {
            color: white;
        }

        .hide-dropdown-icon::after {
            display: none;
        }

    </style>
    <title>Eccopedia</title>
</head>
<body>

    <div>

        @include('eccopedia.navbar')

        @yield('content')

        @include('eccopedia.footer')
    </div>

    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="https://cdn.rtlcss.com/bootstrap/v4.2.1/js/bootstrap.min.js" integrity="sha384-a9xOd0rz8w0J8zqj1qJic7GPFfyMfoiuDjC9rqXlVOcGO/dmRqzMn34gZYDTel8k" crossorigin="anonymous"></script>
    <script>
        $(document).ready(function() {

            $(window).resize(function(){
                if ($(window).width() >= 980){

                    $(".navbar .dropdown-toggle").hover(function () {
                        $(this).parent().toggleClass("show");
                        $(this).parent().find(".dropdown-menu").toggleClass("show");
                    });

                    $( ".navbar .dropdown-menu" ).mouseleave(function() {
                        $(this).removeClass("show");
                    });

                }
            });

        });
    </script>

</body>
</html>
