<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.2.1/css/bootstrap.min.css" integrity="sha384-vus3nQHTD+5mpDiZ4rkEPlnkcyTP+49BhJ4wJeJunw06ZAp+wzzeBPUXr42fi8If" crossorigin="anonymous">
    <title>Tawfeer Masr</title>
</head>
<body>

    <div id="app">

        <div class="container">

            <div class="col-sm-12">

                <h1>أضافة عرض جديد</h1>

                <form>

                    <div class="form-group">
                        <label for="title">العنوان</label>
                        <input required type="text" class="form-control" placeholder="العنوان" name="title" v-model="formData.title">
                    </div>

                    <div class="form-group">
                        <label for="title">التاريخ</label>
                        <input required type="text" class="form-control" placeholder="التاريخ" name="date" v-model="formData.date">
                    </div>

                    <div class="form-group">
                        <label for="title">الصورة الرئيسية</label>
                        <input required type="file" class="form-control" placeholder="الصورة الرئيسية" name="thumbnailImgUrl" @change="handleMainImageChange">
                    </div>

                    <div class="form-group">
                        <label for="title">الصور</label>
                        <input required type="file" class="form-control" placeholder="الصور"  multiple name="imgUrls[]" @change="handleImagesChange">
                    </div>

                    <div class="form-group">
                        <button :disabled="loading" class="btn btn-success" @click.prevent="save" type="submit">
                            حفظ
                        </button>
                    </div>

                </form>

            </div>

            <div class="col-sm-12">

                <h1 class="page-header">العروض</h1>

                <ul v-if="hasOffers">
                    <li v-for="(offer, id) in offers" :key="id">
                        <div class="row mb-2 justify-content-center align-items-center">
                            <div class="col-sm-2 text-center">
                                <img class="img-responsive w-100" :src="offer.thumbnailImgUrl" alt="">
                            </div>
                            <div class="col-sm-8 mb-3">
                                <p class="lead">@{{ offer.title }}
                                    <button @click="remove($event, id)">حذف</button>
                                </p>
                            </div>
                            <div v-if="offer.imgUrls.length > 0" class="col-sm-12">
                                <div class="row mt-3">
                                    <div v-for="(img, index) in offer.imgUrls" class="col-sm-2" :key="index.toString()">
                                        <img class="img-responsive w-100" :src="img.url" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>
                    </li>
                </ul>

            </div>

        </div>

    </div>

    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-storage.js"></script>

    <script>

        // Your web app's Firebase configuration
        var firebaseConfig = {
            apiKey: "AIzaSyB9gn3sPyYqJ6dZCiJIIaHzBeq3aNkUDD0",
            authDomain: "tawfeer-masr-a97c5.firebaseapp.com",
            databaseURL: "https://tawfeer-masr-a97c5.firebaseio.com",
            projectId: "tawfeer-masr-a97c5",
            storageBucket: "tawfeer-masr-a97c5.appspot.com",
            messagingSenderId: "361774488027",
            appId: "1:361774488027:web:312ab0001de503ec956ffe"
        };
        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        new Vue({

            el: '#app',

            data: {

                name: 'Mohamed',

                offers: {},

                hasOffers: false,

                formData: {
                    title: '',
                    date: '',
                    thumbnailImgUrl: null,
                    imgUrls: [],
                    createdAt: null
                },

                loading: false

            },

            mounted: function () {

                let vm = this;

                let databaseRef = firebase.database().ref('offers');

                databaseRef.on("value", function(snapshot) {
                    vm.offers = (snapshot.val());
                    vm.hasOffers =vm.offers ? Object.values(vm.offers).length > 0: false;
                });

            },

            methods: {

                handleMainImageChange: function (e) {

                    this.formData.thumbnailImgUrl = e.target.files;

                },

                handleImagesChange: function (e) {

                    this.formData.imgUrls = e.target.files;

                },

                save () {

                    let vm = this;

                    let data = {
                        createdAt: new Date().getTime(),
                        title: this.formData.title,
                        date: this.formData.date,
                        thumbnailImgUrl: null,
                        imgUrls: []
                    };

                    vm.loading = true;

                    vm.upload(vm.formData.thumbnailImgUrl, false, function (url) {
                        vm.upload(vm.formData.imgUrls, true, function (urls) {

                            urls = urls.map(function (url) {
                                return {
                                    imageName: url,
                                    url: url,
                                };
                            });

                            data['thumbnailImgUrl'] = url;
                            data['imgUrls'] = urls;
                            data['created_at'] = new Date().getTime();

                            let databaseRef = firebase.database().ref('offers');

                            databaseRef.push(data);

                            vm.loading = false;

                        });
                    });

                },

                remove (e, id) {

                    firebase.database().ref('offers/' + id).remove();

                },

                upload (files, array = false, done = null) {

                    let vm = this;

                    let storage = firebase.storage().ref();

                    files = Array.from(files);

                    if (array && files.length > 0) {

                        let filesLength = files.length;

                        let urls = [];

                        files.forEach(function (file) {
                            vm.uploadFile(file, function (url) {
                                urls.push(url);
                                if (urls.length === filesLength) {
                                    done(urls);
                                }
                            });
                        })

                    }

                    if (!array && files.length > 0) {

                        this.uploadFile(files.splice(0,1)[0], done);

                    }

                },

                uploadFile (file, done) {

                    let storage = firebase.storage().ref();

                    let fileRef = storage.child('offers/' + (new Date().getTime()) + '_' + file.name);

                    fileRef.put(file).then(function (snapshot) {
                        fileRef.getDownloadURL().then(function (url) {
                            done(url);
                        });
                    });

                }

            }

        });

    </script>

</body>
</html>
