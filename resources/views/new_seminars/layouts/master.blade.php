@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'talal&marc';

$blue = '#07afde';

@endphp

        <!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

<head>

    <meta name="google-site-verification" content="sEsLaUP9jJEmhx-OSwlcZsCsFm848KQ6PqA8u15KmCQ" />

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Talal Al ajami">
    <title>
        {{ en_ar('STOCKS', 'الأسهم') }}
    </title>

    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
    @if($lang == 'ar' || !$lang)
        <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.2.1/css/bootstrap.min.css" integrity="sha384-vus3nQHTD+5mpDiZ4rkEPlnkcyTP+49BhJ4wJeJunw06ZAp+wzzeBPUXr42fi8If" crossorigin="anonymous">
    @else
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    @endif
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Almarai&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>

    @include('partials.gtag')

    <style lang="scss">

      @font-face {
        font-family: Noor;
        src: url('{{ asset('fonts/new_seminar/Noor Regular.ttf') }}');
      }

      a {
        text-decoration: none !important;
        color: black;
      }

      a:hover {
        color: {{ $blue }};
      }

      *, body {
        font-family: Noor;
      }

      form label {
        color: black !important;
      }

      form input {
        background-color: transparent !important;
        border: none !important;
        border-bottom: 1px solid black !important;
        color: black !important;
      }

      form select {
        background-color: transparent !important;
        border: none !important;
        border-bottom: 1px solid black !important;
        color: black !important;
      }

      ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: black !important;
        opacity: 1; /* Firefox */
      }

      :-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: black !important;
      }

      ::-ms-input-placeholder { /* Microsoft Edge */
        color: black !important;
      }

      .blue {
        color: #07afde;
      }

      .blue-bg {
        background-color: #07afde;
      }

      .content-item:hover {
        color: {{ $blue }};
      }

      div.info-container {
        border: 2px solid black;
      }

      div.info-container:hover {
        border: 2px solid {{$blue}};
      }

      div.info-container:hover i {
        color: {{ $blue }};
      }

      div.info-container:hover p {
        color: {{ $blue }};
      }

      .lecturer-right-image {
        margin-top: -100px;
      }

      @media (max-width: 768px) {
        .lecturer-right-image {
          margin-top: 50px;
        }
      }

      .swiper {
        margin-top: 30px;
        margin-bottom: 30px;
        width: 50%;
        height: 300px;
      }

      .swiper .swiper-slide {
        width: 50%;
        height: 300px;
      }

      @media (max-width: 768px) {
        .swiper {
          width: 100%;
          height: 300px;
        }

        .swiper .swiper-slide {
          width: 100%;
          height: 300px;
        }
      }

    </style>

    <style>

        .countdown {
            font-family: 'Roboto';
            text-transform: uppercase;
        }

        .countdown > div { display: inline-block; }

        .countdown > div > span {
            display: block;
            text-align: center;
        }

        .countdown-container { margin: 0 3px; }

        .countdown-container .countdown-heading {
            font-size: 20px;
            margin: 3px;
            color: black;
        }

        .countdown-container .countdown-value {
            font-size: 50px;
            background: {{ $blue }};
            padding: 10px;
            color: #fff;
            text-shadow: 2px 2px 2px rgba(0,0,0,0.4);
            border-radius:5px;
            box-shadow: 2px 2px 2px rgba(0,0,0,0.4);
        }
    </style>

</head>

<body>

    <div class="navbar">

        <div class="d-none d-lg-block">
            <div class="row p-4">

                <div class="col-sm-4 d-flex {{ isAr() ? 'justify-content-end' : '' }}" style="direction: ltr">
                    <img src="{{ asset('imgs/trade_shares/vi_logo2.png') }}?v=3" style="height: 30px;">

                    <img src="{{ asset('imgs/trade_shares/one_financial.png') }}?v=1" style="height: 30px;">
                </div>

                <div class="col-sm-4 d-flex align-items-center flex-grow-1 justify-content-center">

                    <a href="http://ar.vimarkets.me">
                        <i class="fa fa-globe fa-2x"  style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-3"></div>

                    <a href="https://www.youtube.com/channel/UCxS9RWYyDfbR0YUl_tGbT2w">
                        <i class="fa fa-youtube fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-3"></div>

                    <a href="https://instagram.com/vimarkets?igshid=hglkdbhhk1ef">
                        <i class="fa fa-instagram fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-3"></div>

                    <a href="https://www.linkedin.com/company/vi-markets/">
                        <i class="fa fa-linkedin fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-3"></div>

                    <a href="https://twitter.com/vi_markets?s=11&t=Kac0MbiN_RN839e_Y-uRVg">
                        <i class="fa fa-twitter fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-3"></div>

                    <a href="https://api.whatsapp.com/send?phone=96522256988">
                        <i class="fa fa-whatsapp fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                </div>

                <div class="col-sm-4 d-flex justify-content-end align-items-center">
                    <h5 style="direction: ltr">
                        <i class="fa fa-phone"></i> :
                        +96522256988
                    </h5>

                    <div class="mx-2"></div>

                    <a href="{{ request()->fullUrlWithQuery(['lang' => en_ar('ar', 'en')]) }}" class="btn btn-info pull-left">
                        {{ en_ar('العربية', 'English')  }}
                    </a>
                </div>

            </div>
        </div>

        <div class="d-block d-lg-none">

            <div style="direction: ltr" class="d-flex flex-column justify-content-center align-items-center mt-2 mb-3">
                <img src="{{ asset('imgs/trade_shares/vi_logo2.png') }}?v=2" style="height: 50px;margin-bottom: 10px">

                <img src="{{ asset('imgs/trade_shares/one_financial.png') }}" style="height: 50px;">
            </div>

            <div class="d-flex align-items-center flex-grow-1 justify-content-center mt-2 mb-4">

                <a href="http://ar.vimarkets.me">
                    <i class="fa fa-globe fa-2x" style="color: {{ $blue }}"></i>
                </a>

                <div class="mx-3"></div>

                <a href="https://www.youtube.com/channel/UCxS9RWYyDfbR0YUl_tGbT2w">
                    <i class="fa fa-youtube fa-2x" style="color: {{ $blue }}"></i>
                </a>

                <div class="mx-3"></div>

                <a href="https://instagram.com/vimarkets?igshid=hglkdbhhk1ef">
                    <i class="fa fa-instagram fa-2x" style="color: {{ $blue }}"></i>
                </a>

                <div class="mx-3"></div>

                <a href="https://www.linkedin.com/company/vi-markets/">
                    <i class="fa fa-linkedin fa-2x" style="color: {{ $blue }}"></i>
                </a>

                <div class="mx-3"></div>

                <a href="https://twitter.com/vi_markets?s=11&t=Kac0MbiN_RN839e_Y-uRVg">
                    <i class="fa fa-twitter fa-2x" style="color: {{ $blue }}"></i>
                </a>

                <div class="mx-3"></div>

                <a href="https://api.whatsapp.com/send?phone=96522256988">
                    <i class="fa fa-whatsapp fa-2x" style="color: {{ $blue }}"></i>
                </a>

            </div>

            <div class="flex-grow-1 d-flex justify-content-center mt-2 align-items-center mb-2">
                <h5 style="direction: ltr">
                    <i class="fa fa-phone"></i> :
                    +96522256988
                </h5>

                <div class="mx-2"></div>

                <a href="{{ request()->fullUrlWithQuery(['lang' => en_ar('ar', 'en')]) }}" class="btn btn-info pull-left">
                    {{ en_ar('العربية', 'English')  }}
                </a>
            </div>

        </div>

    </div>

    <div class="d-none d-lg-block mt-4">

        <div class="row">

            <div data-aos="slide-left" class="w-50">

                <img src="{{ asset('imgs/seminar_backgrounds/cover-4 (1).jpg') }}" class="w-100" alt="" style="transform: scaleX({{ isAr() ? 1 : -1 }})">

            </div>

            <div data-aos="slide-right" class="w-50 d-flex justify-content-center">

                <div class="w-75">

                    <div class="text-center mb-4">
                        <h1 class="text-dark" style="font-size: 60px">
                            {{ en_ar('STOCKS', 'الأسهم') }}
                        </h1>
                    </div>

                    @include('partials.validation_errors')
                    @include('partials.success')

                    <form action="{{ route('trading_course.register') }}" method="post">

                        <input type="hidden" name="presenter" value="{{ $presenter }}" />

                        {{ csrf_field() }}

                        <div class="form-group">
                            <input type="text" name="name" required class="form-control" placeholder="{{ en_ar('Full Name', 'الأسم بالكامل') }}">
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-4 mb-2">
                                @include('partials.dial_code')
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="phone" required class="form-control" placeholder="{{ en_ar('Phone', 'رقم الهاتف') }}">
                            </div>
                        </div>

                        <div class="form-group">
                            <input type="email" name="email" required class="form-control" placeholder="{{ en_ar('Email', 'البريد الألكتروني') }}">
                        </div>

                        <div class="text-center mt-4">
                            <h4 class="text-black text-center">
                                <p>{{ en_ar('Are you a client of VI Markets?', 'هل أنت عميل لدينا ؟') }}</p>
                            </h4>
                        </div>

                        <div class="row">
                            <div class="col-sm-6 d-flex justify-content-center">
                                <input type="radio" name="is_client" id="client" value="1">
                                <label for="client">{{ en_ar('Yes', 'نعم') }}</label>
                            </div>
                            <div class="col-sm-6 d-flex justify-content-center">
                                <input type="radio" name="is_client" id="not_client" value="0">
                                <label for="not_client">{{ en_ar('No', 'لا') }}</label>
                            </div>
                        </div>

                        <div class="form-group mt-5">
                            <button type="submit" class="btn btn-dark btn-block">
                                {{ en_ar('Register', 'تسجيل') }}
                            </button>
                        </div>

                    </form>

                </div>

            </div>

        </div>

    </div>

    <div class="d-block d-lg-none mt-4">

        <div class="row">

            <div data-aos="slide-right" class="w-100 d-flex justify-content-center mb-5">

                <div class="w-75">

                    <div class="text-center mb-4">
                        <h1 class="text-dark" style="font-size: 60px">
                            {{ en_ar('STOCKS', 'الأسهم') }}
                        </h1>
                    </div>

                    @include('partials.validation_errors')
                    @include('partials.success')

                    <form action="{{ route('trading_course.register') }}" method="post">

                        <input type="hidden" name="presenter" value="{{ $presenter }}" />

                        {{ csrf_field() }}

                        <div class="form-group">
                            <input type="text" name="name" required class="form-control" placeholder="{{ en_ar('Full Name', 'الأسم بالكامل') }}">
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-4 mb-2">
                                @include('partials.dial_code')
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="phone" required class="form-control" placeholder="{{ en_ar('Phone', 'رقم الهاتف') }}">
                            </div>
                        </div>

                        <div class="form-group">
                            <input type="email" name="email" required class="form-control" placeholder="{{ en_ar('Email', 'البريد الألكتروني') }}">
                        </div>

                        <div class="text-center mt-4">
                            <h4 class="text-black text-center">
                                <p>{{ en_ar('Are you a client of VI Markets?', 'هل أنت عميل لدينا ؟') }}</p>
                            </h4>
                        </div>

                        <div class="row">
                            <div class="col-sm-6 d-flex justify-content-center">
                                <input type="radio" name="is_client" id="client" value="1">
                                <label for="client">{{ en_ar('Yes', 'نعم') }}</label>
                            </div>
                            <div class="col-sm-6 d-flex justify-content-center">
                                <input type="radio" name="is_client" id="not_client" value="0">
                                <label for="not_client">{{ en_ar('No', 'لا') }}</label>
                            </div>
                        </div>

                        <div class="form-group mt-5">
                            <button type="submit" class="btn btn-dark btn-block">
                                {{ en_ar('Register', 'تسجيل') }}
                            </button>
                        </div>

                    </form>

                </div>

            </div>

        </div>

    </div>

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturers', 'المحاضرين') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-6 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/marc.jpg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('MARC Kemsey', 'مارك كيمزي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar('•	Marc Kimsey is Director of award-winning, London stockbroker, Frederick & Oliver. ', '•	مدير شركة فريدريك وأوليفر في لندن الحائزة على عدة جوائز.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	20 Years experience in financial markets.','•	خبرة 20 عاماً في الأسواق المالية') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Specialising in trading UK and US equities.', '•	متخصص في تداول الأسهم في المملكة المتحدة والولايات المتحدة.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Marc’s market commentary and technical analysis has featured in the financial press including Reuters, BBC, The Telegraph and Morningstar.', '•	محلل فني – حلل السوق في عدة صحف بما في ذلك: Reuters, BBC, The Telegraph and Morningstar') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Service the VI Markets Black Accounts.', '•	مدير حسابات بلاك في شركة VI Markets') }}
                    </p>

                </div>

                <div data-aos="fade-left" data-aos-duration="1000" class="col-sm-6 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/talal.jpg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Talal AlAjmi', 'طلال العجمي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar('• Founder and CEO of VI Markets', '•	المؤسس والرئيس التنفيذي لشركة VI Markets') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Member of the Board of Directors of One financial Markets.', '•	عضو مجلس ادارة الوسيط العالمي One financial Markets') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Won many awards and was chosen as one of the top 10 CEOs in Kuwait by CEO Insights.', '•	حاز على العديد من الجوائز واختير كأحد اقوى 10 رؤساء تنفيذيين في الكويت من قبل CEO insights') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• One of the most powerful Arab personalities in the Arabian Business list in 2021 and 2022 in its list of the most influential leaders.', '•	أحد اقوى الشخصيات العربية في قائمة ارابيان بيزنس عام 2021 وعام 2022 في قائمتها للقادة الاكثر نفوذاً وتأثيراً') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Visionary leader of the year Award in 2021 by Entrepreneur Middle East.', '•	صاحب الرؤية الافضل عام 2021 من قبل Entrepreneur Middle East') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• chosen as one of the best achievers in the Gulf countries.', '•	اختير كأحد أفضل الشباب المنجزين على مستوى دول الخليج.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Global Pioneer Award.', '•	حصل على جائزة الرائد العالمي.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Chosen to be on the Arabian Business list of pioneering leaders in its issue dedicated to Expo 2020.', '•	تواجد ضمن قائمة ارابيان بيزنس للقادة الرواد في عددها المخصص لإكسبو 2020.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Best Emerging Trading CEO Award by IFM Awards.', '•	جائزة أفضل رئيس تنفيذي ناشئ للتداول من قبل IFM Awards.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Top 100 CEOs in the Middle East 2022 by Forbes', '•	جاء ضمن قائمة فوربس لأقوى 100 رئيس تنفيذي في الشرق الاوسط للعام 2022.') }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    <div class="d-block d-lg-none mt-4">
        <div data-aos="slide-left" class="w-100">

            <img src="{{ asset('imgs/seminar_backgrounds/cover-4 (1).jpg') }}" class="w-100" alt="">

        </div>
    </div>

    <div data-aos="zoom-in-up" class="mt-4" data-aos-duration="1000" style="border-bottom: 1px solid {{ $blue }};">

        <div class="container py-5">

            <div class="row">

                <div class="col-sm-12 mb-5">
                    <h2 class="text-center blue">{{ en_ar('Seminar Topics', 'محاور الندوة') }}</h2>
                </div>

                @include('new_seminars.partials.content_item', ['number' => 1, 'content' => en_ar('Learn stock trading', 'تعلم تداول الأسهم')])

                @include('new_seminars.partials.content_item', ['number' => 2, 'content' => en_ar('All you need to know about technical analysis', 'كل ما تحتاج معرفته عن التحليل الفني')])

                @include('new_seminars.partials.content_item', ['number' => 3, 'content' => en_ar('Principles of corporate financial analysis', 'مبادئ التحليل المالي للشركات')])

                @include('new_seminars.partials.content_item', ['number' => 4, 'content' => en_ar('Most Active Stocks', 'الأسهم الاكثر نشاطًا')])

                @include('new_seminars.partials.content_item', ['number' => 5, 'content' => en_ar('How to analyze new investment opportunities', 'كيفية تحليل فرص الاستثمار الجديدة')])

                @include('new_seminars.partials.content_item', ['number' => 6, 'content' => en_ar('Black Account', 'حساب Black')])

                @include('new_seminars.partials.content_item', ['number' => 7, 'content' => en_ar('Copy Trading ( Stock )', 'نسخ صفقات الاسهم')])

            </div>

        </div>


    </div>

    <div data-aos="fade-up" class="seminar-info" style="border-bottom: 1px solid {{ $blue }};">
        <div class="container pb-5 mt-5">

            <div class="row">

                <div class="col-sm-12 mb-5 pb-5">
                    <div class='countdown text-center' style="margin: 25px 0" data-date="2022-08-15" data-time="17:00"></div>
                </div>

                <div class="col-sm-4 mb-5">

                    <div style=" position: relative" class="p-4 info-container">

                        <i class="fa fa-4x fa-clock-o" style="position: absolute; top: -45px; background-color: white; padding: 10px"></i>

                        <p class="lead m-0 mt-2">
                            {{ en_ar('5:00 pm Kuwait time', '5:00 مساءاً بتوقيت الكويت') }}
                        </p>

                    </div>

                </div>

                <div class="col-sm-4 mb-5">

                    <div style=" position: relative" class="p-4 info-container">

                        <i class="fa fa-3x fa-calendar" style="position: absolute; top: -40px; background-color: white; padding: 10px"></i>

                        <p class="lead m-0 mt-2">
                            {{ en_ar('From 15 Aug until 20 Aug', 'من 15 اغسطس حتى 20 اغسطس') }}
                        </p>

                    </div>

                </div>

                <div class="col-sm-4 mb-5">

                    <div style=" position: relative" class="p-4 info-container">

                        <i class="fa fa-3x fa-map-marker" style="position: absolute; top: -40px; background-color: white;padding: 10px"></i>

                        <p class="lead m-0 mt-2">
                            The Trading Club
                        </p>

                    </div>

                </div>

            </div>

        </div>
    </div>

    <div class="py-5 blue-bg" style="border-bottom: 1px solid {{ $blue }}">

        <div class="container">

            <div class="row text-white" id="counter-box">

                <div data-aos="fade-left" class="col-sm-4 info-circle d-flex justify-content-center align-items-center flex-column text-center mb-4">

                    <h1 class="font-weight-bold">+<span class="counter" data-number="12">12</span></h1>

                    <h3>{{ en_ar('Years of experience', 'عاما من الخبرة') }}</h3>

                </div>

                <div data-aos="fade-up" class="col-sm-4 info-circle d-flex justify-content-center align-items-center flex-column text-center mb-4">

                    <h1 class="font-weight-bold">+<span class="counter" data-number="10000">10,000</span></h1>

                    <h3>{{ en_ar('Free webinars', 'محاضرات مجانية') }}</h3>

                </div>

                <div data-aos="fade-right" class="col-sm-4 info-circle d-flex justify-content-center align-items-center flex-column text-center">

                    <h1 class="font-weight-bold">+<span class="counter" data-number="30000">30,000</span></h1>

                    <h3>{{ en_ar('Trainers', 'متدرب') }}</h3>

                </div>

            </div>

        </div>

    </div>

    <div class="learn-and-train pb-4" >

        <div class="container mt-5">

            <div class="row mt-5">

                <div class="col-sm-12 d-flex align-items-center">

                    <h1 class="blue font-weight-bold wow slideInRight">
                        <i class="fa fa-align-right"></i>
                        {{ en_ar('Education and Training.', 'التعليم والتدريب') }}
                    </h1>

                </div>

            </div>

        </div>

        <div class="container mt-4">

            <div class="row">

                <div class="col-sm-6 wow slideInRight">

                    <p class="lead">
                        {{ en_ar("Our seminars and training courses meet many clients' needs, starting with basic information about financial markets, how to analyze markets and risk methods.", 'تلبي ندواتنا و دوراتنا التدريبية العديد من احتياجات العملاء ابتداءً من المعلومات المبدئية عن الأسواق المالية وكيفية تحليل الأسواق و أساليب المخاطر.') }}
                    </p>

                </div>

                <div class="col-sm-6 wow slideInLeft">

                    <p class="lead">
                        {{ en_ar("Gain the investment knowledge and experience needed to develop your trading skills with our free advanced education programmes.", 'اكتسب المعرفة الاستثمارية والخبرة اللازمة لتطوير مهارات التداول من خلال برامج التعليم المطورة المجانية لدينا.') }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    <div>

        <!-- Slider main container -->
        <div class="swiper">
            <!-- Additional required wrapper -->
            <div class="swiper-wrapper">
                <!-- Slides -->
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/986A0430-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/986A0426-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/986A0365-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/29e1ca17-7fa4-4691-8c2e-825b2a640cd4-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/5X8A8368-min.jpg') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/DSC00165-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/DSC00212-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/DSC00239-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/IMG-20180425-WA0101-min.jpg') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/IMG_3111-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/IMG_9278-min.JPG') }}'); background-position: center center; background-size: cover"></div>
                <div class="swiper-slide" style="background-image: url('{{ asset('imgs/lectures_slider/IMG_9891-min.jpg') }}'); background-position: center center; background-size: cover"></div>
            </div>
            <!-- If we need pagination -->
            <div class="swiper-pagination"></div>

            <!-- If we need navigation buttons -->
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>

            <!-- If we need scrollbar -->
            <div class="swiper-scrollbar"></div>
        </div>

    </div>

    <div class="footer pb-5 pt-5" style="border-top: 1px solid {{ $blue }}">

        <div class="container">

            <div class="row">

                <div class="col-md-4 mb-3 mb-md-0 d-flex justify-content-center">

                    <a href="https://www.google.com/maps/search/Vi%20Markets%20-%20ONE%20%7C%20FINANCIAL/@29.375036239624023,47.989933013916016,17z?hl=en">
                        <i class="fa fa-map-marker"></i>
                        شرق , برج مزايا 2, الدور العاشر
                    </a>

                </div>

                <div class="col-md-4 mb-3 mb-md-0 d-flex justify-content-center" style="direction: ltr">

                    <a href="https://api.whatsapp.com/send?phone=96522256988">
                        <i class="fa fa-phone"></i>
                        +965 22256988
                    </a>

                </div>

                <div class="col-md-4 mb-3 mb-md-0 d-flex justify-content-center">

                    <a href="https://instagram.com/vimarkets?igshid=hglkdbhhk1ef" class="blue">
                        <i class="fa fa-2x fa-instagram"></i>
                    </a>

                    <div class="mx-2"></div>

                    <a href="https://www.youtube.com/channel/UCxS9RWYyDfbR0YUl_tGbT2w" class="blue">
                        <i class="fa fa-2x fa-youtube"></i>
                    </a>

                    <div class="mx-2"></div>

                    <a href="http://ar.vimarkets.me" class="blue">
                        <i class="fa fa-2x fa-globe"></i>
                    </a>

                    <div class="mx-2"></div>

                    <a href="https://www.linkedin.com/company/vi-markets/">
                        <i class="fa fa-linkedin fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-2"></div>

                    <a href="https://twitter.com/vi_markets?s=11&t=Kac0MbiN_RN839e_Y-uRVg">
                        <i class="fa fa-twitter fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                    <div class="mx-2"></div>

                    <a href="https://api.whatsapp.com/send?phone=96522256988">
                        <i class="fa fa-whatsapp fa-2x" style="color: {{ $blue }}"></i>
                    </a>

                </div>

            </div>

            <div class="d-flex justify-content-center mt-5">
                <p class="text-center">

                    {{ en_ar("CFDs and FX are high risk leveraged products.", "التداول بالعقود مقابل الفروقات والعملات العالمية بنظام الهامش يحمل مخاطر عالية") }}

                </p>
            </div>

        </div>

    </div>

<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="{{ asset('js/jquery.min.js') }}"></script>
<script src="https://cdn.rtlcss.com/bootstrap/v4.2.1/js/bootstrap.min.js" integrity="sha384-a9xOd0rz8w0J8zqj1qJic7GPFfyMfoiuDjC9rqXlVOcGO/dmRqzMn34gZYDTel8k" crossorigin="anonymous"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

<script>

    AOS.init();

    var a = 0;
    $(window).scroll(function () {
        var oTop = $("#counter-box").offset().top - window.innerHeight;
        if (a == 0 && $(window).scrollTop() > oTop) {
            $(".counter").each(function () {
                var $this = $(this),
                    countTo = $this.attr("data-number");
                $({
                    countNum: $this.text()
                }).animate(
                    {
                        countNum: countTo
                    },

                    {
                        duration: 2000,
                        easing: "swing",
                        step: function () {
                            //$this.text(Math.ceil(this.countNum));
                            $this.text(
                                Math.ceil(this.countNum).toLocaleString("en")
                            );
                        },
                        complete: function () {
                            $this.text(
                                Math.ceil(this.countNum).toLocaleString("en")
                            );
                            //alert('finished');
                        }
                    }
                );
            });
            a = 1;
        }
    });


    const swiper = new Swiper('.swiper', {
        // Optional parameters
        direction: 'horizontal',
        loop: true,

        // If we need pagination
        pagination: {
            el: '.swiper-pagination',
        },

        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        // And if we need scrollbar
        scrollbar: {
            el: '.swiper-scrollbar',
        },
    });

</script>

<script>

    (function ( $ ) {
        function pad(n) {
            return (n < 10) ? ("0" + n) : n;
        }

        $.fn.showclock = function() {

            var currentDate=new Date();
            var fieldDate=$(this).data('date').split('-');
            var futureDate=new Date(fieldDate[0],fieldDate[1]-1,fieldDate[2]);
            var seconds=futureDate.getTime() / 1000 - currentDate.getTime() / 1000;

            if(seconds<=0 || isNaN(seconds)){
                this.hide();
                return this;
            }

            var days=Math.floor(seconds/86400);
            seconds=seconds%86400;

            var hours=Math.floor(seconds/3600);
            seconds=seconds%3600;

            var minutes=Math.floor(seconds/60);
            seconds=Math.floor(seconds%60);

            var html="";

            if(days!=0){
                html+="<div class='countdown-container days'>"
                html+="<span class='countdown-heading days-top'><?php echo e(en_ar('Days', 'يوم')); ?></span>";
                html+="<span class='countdown-value days-bottom'>"+pad(days)+"</span>";
                html+="</div>";
            }

            html+="<div class='countdown-container hours'>"
            html+="<span class='countdown-heading hours-top'><?php echo e(en_ar('Hours', 'ساعة')); ?></span>";
            html+="<span class='countdown-value hours-bottom'>"+pad(hours)+"</span>";
            html+="</div>";

            html+="<div class='countdown-container minutes'>"
            html+="<span class='countdown-heading minutes-top'><?php echo e(en_ar('Minutes', 'دقيقة')); ?></span>";
            html+="<span class='countdown-value minutes-bottom'>"+pad(minutes)+"</span>";
            html+="</div>";

            html+="<div class='countdown-container seconds'>"
            html+="<span class='countdown-heading seconds-top'><?php echo e(en_ar('Seconds', 'ثواني')); ?></span>";
            html+="<span class='countdown-value seconds-bottom'>"+pad(seconds)+"</span>";
            html+="</div>";

            this.html(html);
        };

        $.fn.countdown = function() {
            var el=$(this);
            el.showclock();
            setInterval(function(){
                el.showclock();
            },1000);

        }

    }(jQuery));

    jQuery(document).ready(function(){
        if(jQuery(".countdown").length>0)
            jQuery(".countdown").countdown();
    });

</script>

</body>

</html>
