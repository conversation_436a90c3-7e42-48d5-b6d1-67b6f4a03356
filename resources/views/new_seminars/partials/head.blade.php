<head>

    <meta name="google-site-verification" content="sEsLaUP9jJEmhx-OSwlcZsCsFm848KQ6PqA8u15KmCQ" />

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Talal Al ajami">
    <title>
        {{ $title }}
    </title>

    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
    @if($lang == 'ar' || !$lang)
        <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.2.1/css/bootstrap.min.css" integrity="sha384-vus3nQHTD+5mpDiZ4rkEPlnkcyTP+49BhJ4wJeJunw06ZAp+wzzeBPUXr42fi8If" crossorigin="anonymous">
    @else
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    @endif
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Almarai&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>

    <script src="https://cdn.jsdelivr.net/npm/color-calendar/dist/bundle.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/color-calendar/dist/css/theme-basic.css"/>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    @if(isset($presenter) && in_array($presenter, ['trading-seminar', 'trading-seminar-bahrain']))
        <!-- Meta Pixel Code -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1593732618168435');
            fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1" style="display:none"
                       src="https://www.facebook.com/tr?id=1593732618168435&ev=PageView&noscript=1"
            /></noscript>
        <!-- End Meta Pixel Code -->
    @endif

    @include('partials.gtag')

    <style lang="scss">

      @font-face {
        font-family: Noor;
        src: url('{{ asset('fonts/new_seminar/29LTBukraVF-08lrdo.ttf') }}');
      }

      .fc-bg-event {
        background-color: {{ $blue }} !important;
        opacity: 1; !important;
        cursor: pointer; !important;
      }

      @media (max-width: 789px) {
        .fc-event-title {
          color: white; !important;
          font-size: 12px; !important;
        }
      }

      .fc .fc-bg-event {
        opacity: 1; !important;
      }

      .fc-event-title {
        color: white; !important;
        font-weight: bold;
      }

      a {
        text-decoration: none !important;
        color: black;
      }

      a:hover {
        color: {{ $blue }};
      }

      *, body {
        font-family: Noor;
      }

      form label {
        color: black !important;
      }

      form input {
        background-color: transparent !important;
        border: none !important;
        border-bottom: 1px solid black !important;
        color: black !important;
      }

      form select {
        background-color: transparent !important;
        border: none !important;
        border-bottom: 1px solid black !important;
        color: black !important;
      }

      ::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: black !important;
        opacity: 1; /* Firefox */
      }

      :-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: black !important;
      }

      ::-ms-input-placeholder { /* Microsoft Edge */
        color: black !important;
      }

      .blue {
        color: #07afde;
      }

      .blue-bg {
        background-color: #07afde;
      }

      .content-item:hover {
        color: {{ $blue }};
      }

      div.info-container {
        border: 2px solid black;
      }

      div.info-container:hover {
        border: 2px solid {{$blue}};
      }

      div.info-container:hover i {
        color: {{ $blue }};
      }

      div.info-container:hover p {
        color: {{ $blue }};
      }

      .lecturer-right-image {
        margin-top: -100px;
      }

      @media (max-width: 768px) {
        .lecturer-right-image {
          margin-top: 50px;
        }
      }

      .swiper {
        margin-top: 30px;
        margin-bottom: 30px;
        width: 50%;
        height: 300px;
      }

      .swiper .swiper-slide {
        width: 50%;
        height: 300px;
      }

      @media (max-width: 768px) {
        .swiper {
          width: 100%;
          height: 300px;
        }

        .swiper .swiper-slide {
          width: 100%;
          height: 300px;
        }
      }

      .color-calendar.basic .calendar__days .calendar__day-selected .calendar__day-box {
        background-color: {{ $blue }};
      }

      .color-calendar .calendar__days .calendar__day-event .calendar__day-bullet {
        width: 10px;
        height: 10px;
        background-color: {{ $blue }};
      }

      .calendar__day-today .calendar__day-box {
        background-color: transparent !important;
        box-shadow: none !important;
      }

      .calendar__day-event.calendar__day-selected {
        background-color: transparent !important;
        box-shadow: none !important;
      }

      .calendar__day-today .calendar__day-text {
        color: black !important;
      }

    </style>

    <style>

        .countdown {
            font-family: 'Roboto';
            text-transform: uppercase;
        }

        .countdown > div { display: inline-block; }

        .countdown > div > span {
            display: block;
            text-align: center;
        }

        .countdown-container { margin: 0 3px; }

        .countdown-container .countdown-heading {
            font-size: 20px;
            margin: 3px;
            color: black;
        }

        .countdown-container .countdown-value {
            font-size: 50px;
            background: {{ $blue }};
            padding: 10px;
            color: #fff;
            text-shadow: 2px 2px 2px rgba(0,0,0,0.4);
            border-radius:5px;
            box-shadow: 2px 2px 2px rgba(0,0,0,0.4);
        }

        .select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__display {
            color: black !important;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border: 2px solid #d22d3b !important;
        }

        .select2-container--default .select2-selection--multiple {
            border: 2px solid #d22d3b !important;
        }

        .select2-container .select2-selection--multiple {
            min-height: 40px !important;
        }

        .select2-container .select2-search--inline .select2-search__field {
            height: 25px;
        }
    </style>

</head>
<script>
    import Placeholder from "../../../../public/js/select2-4.0.3/docs/_includes/options/selections/placeholder.html";
    export default {
        components: {Placeholder}
    }
</script>
