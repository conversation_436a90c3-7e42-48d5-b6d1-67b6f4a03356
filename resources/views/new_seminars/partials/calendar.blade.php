<div class="py-5" style="border-top: 1px solid {{ $blue }};border-bottom: 1px solid {{ $blue }}">
    <div class="container">
        <div class="row justify-content-center flex-column align-items-center">
            <h2 class="mb-5 text-center blue">
                {{ en_ar('Seminars Table', 'جدول المحاضرات') }}
            </h2>

            <div dir="ltr">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
</div>

<script>

    document.addEventListener('DOMContentLoaded', function () {

        let calendar = new Calendar({
            id: "#calendar",
            eventsData: @json($seminarEvents),
            selectedDateClicked (currentDate, filteredMonthEvents) {
                if (filteredMonthEvents.length > 0) {
                    window.location = filteredMonthEvents[0].url;
                }
            }
        });

        $('.calendar__day-event').on('click', function () {
            let day = $(this).find('.calendar__day-text').text();
            let events = calendar.getEventsData();
            let event = events.find((event) => event.day.toString() === day.toString());
            if (event && event.url) {
                window.location = event.url;
            }
        })

    });

    {{--document.addEventListener('DOMContentLoaded', function() {--}}
    {{--    var calendarEl = document.getElementById('calendar');--}}
    {{--    var calendar = new FullCalendar.Calendar(calendarEl, {--}}
    {{--        initialView: 'dayGridMonth',--}}
    {{--        locale: '{{ request()->get('lang') ?? 'ar' }}',--}}
    {{--        eventClick (info) {--}}
    {{--            if (info.event.url) {--}}
    {{--                window.location = info.event.url;--}}
    {{--            }--}}
    {{--        },--}}
    {{--        events: [--}}
    {{--            {--}}
    {{--                title: 'نظرية الفيبوناتشي',--}}
    {{--                start: '2022-08-10',--}}
    {{--                end: '2022-08-10',--}}
    {{--                interactive: true,--}}
    {{--                url: '{{ url('/seminars/trading-course/ahmed-moaty1') }}',--}}
    {{--                display: 'background',--}}
    {{--                textColor: '#fff'--}}
    {{--            },--}}
    {{--            {--}}
    {{--                title: 'ورشة الهارمونيك',--}}
    {{--                start: '2022-08-17',--}}
    {{--                end: '2022-08-17',--}}
    {{--                interactive: true,--}}
    {{--                url: '{{ url('/seminars/trading-course/bashar') }}',--}}
    {{--                display: 'background',--}}
    {{--                textColor: '#fff'--}}
    {{--            },--}}
    {{--            {--}}
    {{--                title: 'الاشيموكو',--}}
    {{--                start: '2022-08-16',--}}
    {{--                end: '2022-08-16',--}}
    {{--                interactive: true,--}}
    {{--                url: '{{ url('/seminars/trading-course/ranim') }}',--}}
    {{--                display: 'background',--}}
    {{--                textColor: '#fff'--}}
    {{--            },--}}
    {{--            {--}}
    {{--                title: 'منصة التداول MT4',--}}
    {{--                start: '2022-08-11',--}}
    {{--                end: '2022-08-11',--}}
    {{--                interactive: true,--}}
    {{--                url: '{{ url('/seminars/trading-course/rania') }}',--}}
    {{--                display: 'background',--}}
    {{--                textColor: '#fff'--}}
    {{--            },--}}
    {{--        ]--}}
    {{--    });--}}
    {{--    calendar.render();--}}
    // });

</script>
