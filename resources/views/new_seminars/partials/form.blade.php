<div class="d-none d-lg-block mt-4">

    <div class="row">

        <div data-aos="slide-left" class="w-50">

            <img src="{{ asset('imgs/seminar_backgrounds/cover-4 (1).jpg') }}" class="w-100" alt="" style="transform: scaleX({{ isAr() ? 1 : -1 }})">

        </div>

        <div data-aos="slide-right" class="w-50 d-flex justify-content-center">

            <div class="w-75">

                <div class="text-center mb-4">
                    <h1 class="text-dark" style="font-size: 60px">
                        {{ $title }}
                    </h1>
                </div>

                @include('partials.validation_errors')
                @include('partials.success')

                <form action="{{ route('trading_course.register') }}" method="post">

                    <input type="hidden" name="presenter" value="{{ $presenter }}" />

                    {{ csrf_field() }}

                    <div class="form-group">
                        <input type="text" name="name" required class="form-control" placeholder="{{ en_ar('Full Name', 'الأسم بالكامل') }}">
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-4 mb-2">
                            @include('partials.dial_code')
                        </div>
                        <div class="col-sm-8">
                            <input type="text" name="phone" required class="form-control" placeholder="{{ en_ar('Phone', 'رقم الهاتف') }}">
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="email" name="email" required class="form-control" placeholder="{{ en_ar('Email', 'البريد الألكتروني') }}">
                    </div>

                    <div class="text-center mt-4">
                        <h4 class="text-black text-center">
                            <p>{{ en_ar('Are you a client of VI Markets?', 'هل أنت عميل لدينا ؟') }}</p>
                        </h4>
                    </div>

                    <div class="row">
                        <div class="col-sm-6 d-flex justify-content-center">
                            <input type="radio" name="is_client" id="client" value="1">
                            <label for="client">{{ en_ar('Yes', 'نعم') }}</label>
                        </div>
                        <div class="col-sm-6 d-flex justify-content-center">
                            <input type="radio" name="is_client" id="not_client" value="0">
                            <label for="not_client">{{ en_ar('No', 'لا') }}</label>
                        </div>
                    </div>

                    <div class="form-group mt-5">
                        <button type="submit" class="btn btn-dark btn-block">
                            {{ en_ar('Register', 'تسجيل') }}
                        </button>
                    </div>

                </form>

            </div>

        </div>

    </div>

</div>

<div class="d-block d-lg-none mt-4">

    <div class="row">

        <div data-aos="slide-right" class="w-100 d-flex justify-content-center mb-5">

            <div class="w-75">

                <div class="text-center mb-4">
                    <h1 class="text-dark" style="font-size: 60px">
                        {{ $title }}
                    </h1>
                </div>

                @include('partials.validation_errors')
                @include('partials.success')

                <form action="{{ route('trading_course.register') }}" method="post">

                    <input type="hidden" name="presenter" value="{{ $presenter }}" />

                    {{ csrf_field() }}

                    <div class="form-group">
                        <input type="text" name="name" required class="form-control" placeholder="{{ en_ar('Full Name', 'الأسم بالكامل') }}">
                    </div>

                    <div class="form-group row">
                        <div class="col-sm-4 mb-2">
                            @include('partials.dial_code')
                        </div>
                        <div class="col-sm-8">
                            <input type="text" name="phone" required class="form-control" placeholder="{{ en_ar('Phone', 'رقم الهاتف') }}">
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="email" name="email" required class="form-control" placeholder="{{ en_ar('Email', 'البريد الألكتروني') }}">
                    </div>

                    <div class="text-center mt-4">
                        <h4 class="text-black text-center">
                            <p>{{ en_ar('Are you a client of VI Markets?', 'هل أنت عميل لدينا ؟') }}</p>
                        </h4>
                    </div>

                    <div class="row">
                        <div class="col-sm-6 d-flex justify-content-center">
                            <input type="radio" name="is_client" id="client" value="1">
                            <label for="client">{{ en_ar('Yes', 'نعم') }}</label>
                        </div>
                        <div class="col-sm-6 d-flex justify-content-center">
                            <input type="radio" name="is_client" id="not_client" value="0">
                            <label for="not_client">{{ en_ar('No', 'لا') }}</label>
                        </div>
                    </div>

                    <div class="form-group mt-5">
                        <button type="submit" class="btn btn-dark btn-block">
                            {{ en_ar('Register', 'تسجيل') }}
                        </button>
                    </div>

                </form>

            </div>

        </div>

    </div>

</div>
