<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="{{ asset('js/jquery.min.js') }}"></script>
<script src="https://cdn.rtlcss.com/bootstrap/v4.2.1/js/bootstrap.min.js" integrity="sha384-a9xOd0rz8w0J8zqj1qJic7GPFfyMfoiuDjC9rqXlVOcGO/dmRqzMn34gZYDTel8k" crossorigin="anonymous"></script>
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>


<script>

    AOS.init();

    var a = 0;
    $(window).scroll(function () {
        var oTop = $("#counter-box").offset().top - window.innerHeight;
        if (a == 0 && $(window).scrollTop() > oTop) {
            $(".counter").each(function () {
                var $this = $(this),
                    countTo = $this.attr("data-number");
                $({
                    countNum: $this.text()
                }).animate(
                    {
                        countNum: countTo
                    },

                    {
                        duration: 2000,
                        easing: "swing",
                        step: function () {
                            //$this.text(Math.ceil(this.countNum));
                            $this.text(
                                Math.ceil(this.countNum).toLocaleString("en")
                            );
                        },
                        complete: function () {
                            $this.text(
                                Math.ceil(this.countNum).toLocaleString("en")
                            );
                            //alert('finished');
                        }
                    }
                );
            });
            a = 1;
        }
    });


    const swiper = new Swiper('.swiper', {
        // Optional parameters
        direction: 'horizontal',
        loop: true,

        // If we need pagination
        pagination: {
            el: '.swiper-pagination',
        },

        // Navigation arrows
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        // And if we need scrollbar
        scrollbar: {
            el: '.swiper-scrollbar',
        },
    });

</script>

<script>

    (function ( $ ) {
        function pad(n) {
            return (n < 10) ? ("0" + n) : n;
        }

        $.fn.showclock = function() {

            var currentDate=new Date();
            var fieldDate=$(this).data('date').split('-');
            var fieldTime=$(this).data('time').split(':');
            var futureDate=new Date(fieldDate[0],fieldDate[1]-1,fieldDate[2], fieldTime[0], fieldTime[1]);
            var seconds=futureDate.getTime() / 1000 - currentDate.getTime() / 1000;

            if(seconds<=0 || isNaN(seconds)){
                this.hide();
                return this;
            }

            var days=Math.floor(seconds/86400);
            seconds=seconds%86400;

            var hours=Math.floor(seconds/3600);
            seconds=seconds%3600;

            var minutes=Math.floor(seconds/60);
            seconds=Math.floor(seconds%60);

            var html="";

            if(days!=0){
                html+="<div class='countdown-container days'>"
                html+="<span class='countdown-heading days-top'><?php echo e(en_ar('Days', 'يوم')); ?></span>";
                html+="<span class='countdown-value days-bottom'>"+pad(days)+"</span>";
                html+="</div>";
            }

            html+="<div class='countdown-container hours'>"
            html+="<span class='countdown-heading hours-top'><?php echo e(en_ar('Hours', 'ساعة')); ?></span>";
            html+="<span class='countdown-value hours-bottom'>"+pad(hours)+"</span>";
            html+="</div>";

            html+="<div class='countdown-container minutes'>"
            html+="<span class='countdown-heading minutes-top'><?php echo e(en_ar('Minutes', 'دقيقة')); ?></span>";
            html+="<span class='countdown-value minutes-bottom'>"+pad(minutes)+"</span>";
            html+="</div>";

            html+="<div class='countdown-container seconds'>"
            html+="<span class='countdown-heading seconds-top'><?php echo e(en_ar('Seconds', 'ثواني')); ?></span>";
            html+="<span class='countdown-value seconds-bottom'>"+pad(seconds)+"</span>";
            html+="</div>";

            this.html(html);
        };

        $.fn.countdown = function() {
            var el=$(this);
            el.showclock();
            setInterval(function(){
                el.showclock();
            },1000);

        }

    }(jQuery));

    jQuery(document).ready(function(){
        if(jQuery(".countdown").length>0)
            jQuery(".countdown").countdown();
    });

</script>
