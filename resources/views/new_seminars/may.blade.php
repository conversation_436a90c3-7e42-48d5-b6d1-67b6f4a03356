@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'may';

$blue = '#07afde';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'مؤشر أسعار المستهلكين CPI '
])

<body>

    @include('new_seminars.partials.navbar')

    @include('new_seminars.partials.form', [
        'title' => en_ar('Consumer Price Index CPI', 'مؤشر أسعار المستهلكين CPI ')
    ])

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturer', 'المحاضر') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-12 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/seminars/may.jpeg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Mrs. May bin khadraa', 'أ/مي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar("Independent analyst at VI Markets.", "محللة مستقل في VI Markets") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("Anchor at CNBC Arabia channel", "مذيعة في قناة CNBC عربية") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("Economic Analyst, Director of Dialogue Sessions since 2016.", "محللة اقتصادية، ومديرة جلسات حوارية منذ العام 2016") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("Specialist in financial markets.", "مختصة في الأسواق المالية العالمية") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("She holds a Bachelor's degree in Business Administration, specializing in Banking and Finance.", "حائزة على شهادة بكالوريوس في ادارة الأعمال اختصاص بنوك وتمويل") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("Certificate in International Studies from the University of Delaware in the United States.", "شهادة في الدراسات الدولية من جامعة ديلاوير في الولايات المتحدة الأمريكية") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("She is currently studying to obtain a certified financial analyst certificate from CFA in the second stage.", "تدرس حاليا للحصول على شهادة محلل مالي معتمد CFA في المرحلة الثانية") }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    @include('new_seminars.partials.cover')

{{--    @include('new_seminars.partials.seminar_topics', [--}}
{{--        'topics' => [--}}
{{--            en_ar('Practical application for phones and computers', 'ورشة وتطبيق عملي للهواتف والكمبيوتر'),--}}
{{--            en_ar('The Quantities', 'بيان الكمية'),--}}
{{--            en_ar('The Margin', 'الهامش'),--}}
{{--            en_ar('Drawing tools', 'ادوات الرسم'),--}}
{{--            en_ar('Profit and loss', 'الربح '),--}}
{{--            en_ar('Deal details', 'تفاصيل '),--}}
{{--            en_ar('The spread', 'السبريد'),--}}
{{--        ]--}}
{{--    ])--}}

    @include('new_seminars.partials.seminar_info', [
        'date' => '2023-10-29',
        'time' => '18:00',
        'dateFormatted' => en_ar('29 Oct', '29 أكتوبر'),
        'timeFormatted' => en_ar('6:00 pm Kuwait time', '6:00 مساءاً بتوقيت الكويت'),
        'location' => 'Zoom',
    ])

    @include('new_seminars.partials.calendar')

    @include('new_seminars.partials.numbers')

    @include('new_seminars.partials.learn&train')

    @include('new_seminars.partials.slider')

    @include('new_seminars.partials.t&c', ['presenter' => $presenter])

    @include('new_seminars.partials.footer')

    @include('new_seminars.partials.scripts')

</body>

</html>
