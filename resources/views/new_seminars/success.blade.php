@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'rania3';

$blue = '#e30631';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'تم التسجيل بنجاح'
])

<body>

@include('new_seminars.partials.bashar10days-navbar')

    <div style="height: calc(100vh - 300px); display: flex; justify-content: center; align-items: center" class="text-center">

        <div class="container w-100">
            <div class="row">
                <div class="col-sm-12">
                    @include('partials.success')
                </div>
            </div>
        </div>

    </div>

@include('new_seminars.partials.scripts')

</body>

</html>
