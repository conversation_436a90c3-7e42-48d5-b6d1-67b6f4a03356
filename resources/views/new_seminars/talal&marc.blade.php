@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'talal&marc';

$blue = '#07afde';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'الاسهم',
])

<body>


    @include('new_seminars.partials.navbar', [
        'switchLocale' => false
    ])

    @include('new_seminars.partials.form', [
        'title' => en_ar('STOCKS', 'الأسهم')
    ])

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturers', 'المحاضرين') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-6 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/marc.jpg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('MARC Kemsey', 'مارك كيمزي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar('•	Marc Kimsey is Director of award-winning, London stockbroker, Frederick & Oliver. ', '•	مدير شركة فريدريك وأوليفر في لندن الحائزة على عدة جوائز.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	20 Years experience in financial markets.','•	خبرة 20 عاماً في الأسواق المالية') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Specialising in trading UK and US equities.', '•	متخصص في تداول الأسهم في المملكة المتحدة والولايات المتحدة.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Marc’s market commentary and technical analysis has featured in the financial press including Reuters, BBC, The Telegraph and Morningstar.', '•	محلل فني – حلل السوق في عدة صحف بما في ذلك: Reuters, BBC, The Telegraph and Morningstar') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('•	Service the VI Markets Black Accounts.', '•	مدير حسابات بلاك في شركة VI Markets') }}
                    </p>

                </div>

                <div data-aos="fade-left" data-aos-duration="1000" class="col-sm-6 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/talal.jpg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Talal AlAjmi', 'طلال العجمي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar('• Founder and CEO of VI Markets', '•	المؤسس والرئيس التنفيذي لشركة VI Markets') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Member of the Board of Directors of One financial Markets.', '•	عضو مجلس ادارة الوسيط العالمي One financial Markets') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Won many awards and was chosen as one of the top 10 CEOs in Kuwait by CEO Insights.', '•	حاز على العديد من الجوائز واختير كأحد اقوى 10 رؤساء تنفيذيين في الكويت من قبل CEO insights') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• One of the most powerful Arab personalities in the Arabian Business list in 2021 and 2022 in its list of the most influential leaders.', '•	أحد اقوى الشخصيات العربية في قائمة ارابيان بيزنس عام 2021 وعام 2022 في قائمتها للقادة الاكثر نفوذاً وتأثيراً') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Visionary leader of the year Award in 2021 by Entrepreneur Middle East.', '•	صاحب الرؤية الافضل عام 2021 من قبل Entrepreneur Middle East') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• chosen as one of the best achievers in the Gulf countries.', '•	اختير كأحد أفضل الشباب المنجزين على مستوى دول الخليج.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Global Pioneer Award.', '•	حصل على جائزة الرائد العالمي.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Chosen to be on the Arabian Business list of pioneering leaders in its issue dedicated to Expo 2020.', '•	تواجد ضمن قائمة ارابيان بيزنس للقادة الرواد في عددها المخصص لإكسبو 2020.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Best Emerging Trading CEO Award by IFM Awards.', '•	جائزة أفضل رئيس تنفيذي ناشئ للتداول من قبل IFM Awards.') }}
                    </p>

                    <p class="lead">
                        {{ en_ar('• Top 100 CEOs in the Middle East 2022 by Forbes', '•	جاء ضمن قائمة فوربس لأقوى 100 رئيس تنفيذي في الشرق الاوسط للعام 2022.') }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    @include('new_seminars.partials.cover')

    @include('new_seminars.partials.seminar_topics', [
        'topics' => [
            en_ar('Learn stock trading', 'تعلم تداول الأسهم'),
            en_ar('All you need to know about technical analysis', 'كل ما تحتاج معرفته عن التحليل الفني'),
            en_ar('Principles of corporate financial analysis', 'مبادئ التحليل المالي للشركات'),
            en_ar('Most Active Stocks', 'الأسهم الاكثر نشاطًا'),
            en_ar('How to analyze new investment opportunities', 'كيفية تحليل فرص الاستثمار الجديدة'),
            en_ar('Black Account', 'حساب Black'),
            en_ar('Copy Trading ( Stock )', 'نسخ صفقات الاسهم'),
        ]
    ])

    @include('new_seminars.partials.seminar_info', [
        'date' => '2022-08-15',
        'time' => '17:00',
        'dateFormatted' => en_ar('From 15 Aug until 19 Aug', 'من 15 اغسطس حتى 19 اغسطس'),
        'timeFormatted' => en_ar('5:00 pm Kuwait time', '5:00 مساءاً بتوقيت الكويت'),
        'location' => 'The Trading Club',
    ])

    @include('new_seminars.partials.calendar')

    @include('new_seminars.partials.numbers')

    @include('new_seminars.partials.learn&train')

    @include('new_seminars.partials.slider')

    @include('new_seminars.partials.t&c', ['presenter' => $presenter])

    @include('new_seminars.partials.footer')

    @include('new_seminars.partials.scripts')

</body>

</html>
