@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'bashar2';

$blue = '#07afde';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'استراتيجية المحور الاساسي'
])

<body>

    @include('new_seminars.partials.navbar')

    @include('new_seminars.partials.form', [
        'title' => en_ar('Pivot Point Strategy', 'استراتيجية المحور الاساسي')
    ])

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturer', 'المحاضر') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-12 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/seminars/bashar.png') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Mr. Bashar Al-Asfour', 'أ.بشار العصفور') }}
                    </div>

                    <p class="lead">
                        {{ en_ar("Director of development and training department at VI Markets.", " مدير التطوير والتدريب في شركة VI Markets") }}
                    </p>

                    <p class="lead">
                        {{ en_ar("Financial Analyst and Economic Consultant, Certified Trainer and Member of the American Board (BITA).", "محلل مالي ومستشار اقتصادي، حاصل على إجازة مدرب مدربين وعضوا في ‏البورد الأمريكي (BITA)") }}
                    </p>

                    <p class="lead">
                        {{ en_ar(" Obtained a certificate \"Human Behavior in Trading\" (Alison Edgar) and ways to succeed in small projects", " حاصل على شهادة سلوك البشر في التداول (Alison Edgar )وطرق النجاح في المشاريع الصغيرة.") }}
                    </p>

                    <p class="lead">
                        {{ en_ar("Expert in global markets and local stock exchange since 2002, global markets coach since 2009", "خبرة في الاسواق العالمية والبورصة المحلية منذ عام 2002،  مدرب الاسواق العالميه منذ عام 2009") }}
                    </p>

                    <p class="lead">
                        {{ en_ar("Author of the book Persuasion is easy if done smart.", "") }}
                    </p>

                    <p class="lead">
                        {{ en_ar("Author of the book the Absolute Guide in the World of Global Markets and Forex.", "مؤلف كتاب الاقناع سهل اذا تم بذكاء.مؤلف كتاب الدليل المطلق في عالم الاسواق العالمية والفوركس.") }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    @include('new_seminars.partials.cover')

{{--    @include('new_seminars.partials.seminar_topics', [--}}
{{--        'topics' => [--}}
{{--            en_ar('What is the appropriate amount for the portfolio?', 'الكمية المناسبة للمحفظة'),--}}
{{--            en_ar('Profit and loss management.', 'ادارة الأرباح والخسائر'),--}}
{{--            en_ar('Preparing a capital management plan.', 'عمل خطة لادارة رأس المال'),--}}
{{--            en_ar('Swing Trade Strategy', 'استراتيجية swing Trade'),--}}
{{--        ]--}}
{{--    ])--}}

    @include('new_seminars.partials.seminar_info', [
        'date' => '2022-12-20',
        'time' => '18:00',
        'dateFormatted' => en_ar('20 Dec', '20 ديسمبر'),
        'timeFormatted' => en_ar('5:00 pm Kuwait time', '5:00 مساءاً بتوقيت الكويت'),
        'location' => 'The Trading Club',
    ])

    {{--    @include('new_seminars.partials.calendar')--}}

    @include('new_seminars.partials.numbers')

    @include('new_seminars.partials.learn&train')

    @include('new_seminars.partials.slider')

    @include('new_seminars.partials.t&c', ['presenter' => $presenter])

    @include('new_seminars.partials.footer')

    @include('new_seminars.partials.scripts')

</body>

</html>
