@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'm&m';

$blue = '#07afde';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'أساسيات التداول من الألف الى الياء '
])

<body>

    @include('new_seminars.partials.navbar')

    @include('new_seminars.partials.form', [
        'title' => en_ar('The basics of trading from A to Z', 'أساسيات التداول من الألف الى الياء ')
    ])

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturer', 'المحاضر') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-12 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/seminars/mohammad.jpeg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Mohamed', 'محمد آل عبدالسلام') }}
                    </div>

                    <p class="lead">
                        {{ en_ar('Certified Trainer at VI Markets', 'مدرب معتمد لدى VI Markets') }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    @include('new_seminars.partials.cover')

{{--    @include('new_seminars.partials.seminar_topics', [--}}
{{--        'topics' => [--}}
{{--            en_ar('Risk management workshop.', 'ورشة عمل في ادارة المخاطر'),--}}
{{--            en_ar('How to deal with the markets.', ' طريقة التعامل مع الاسواق'),--}}
{{--            en_ar('How to seize investment opportunities.', 'كيفية اقتناص الفرص الاستثمارية والاستعداد لها'),--}}
{{--        ]--}}
{{--    ])--}}

    @include('new_seminars.partials.seminar_info', [
        'date' => '2023-07-09',
        'time' => '18:00',
        'dateFormatted' => en_ar('9 July', '9 يوليو'),
        'timeFormatted' => en_ar('6:00 pm Kuwait time', '6:00 مساءاً بتوقيت الكويت'),
        'location' => 'Zoom',
    ])


    @include('new_seminars.partials.calendar')

    @include('new_seminars.partials.numbers')

    @include('new_seminars.partials.learn&train')

    @include('new_seminars.partials.slider')

    @include('new_seminars.partials.t&c', ['presenter' => $presenter])

    @include('new_seminars.partials.footer')

    @include('new_seminars.partials.scripts')

</body>

</html>
