@php

    $theme = isset($theme) ? $theme : 'white';

    $lang = request()->get('lang') ?? 'ar';

    function en_ar ($en , $ar) {

        $lang = request()->get('lang') ?? 'ar';

        if ($lang == 'en') {
            return $en;
        }

        if ($lang == 'ar') {
            return $ar;
        }

    }

    function isAr () {
        $lang = request()->get('lang') ?? 'ar';

        return $lang == 'ar';
    }

$presenter = 'rania3';

$blue = '#07afde';

@endphp

<!doctype html>
<html lang="{{ $lang ? $lang : 'ar' }}" dir="{{ $lang == 'en' ? 'ltr' : 'rtl' }}">

@include('new_seminars.partials.head', [
    'title' => 'منصة التداول MT4'
])

<body>

    @include('new_seminars.partials.navbar')

    @include('new_seminars.partials.form', [
        'title' => en_ar('MT4 Platform', 'منصة التداول MT4')
    ])

    <div class="pt-5 lecturers">

        <h1 data-aos="fade-down" data-aos-duration="1000" class="text-center blue mt-4">
            {{ en_ar('Lecturer', 'المحاضر') }}
        </h1>

        <div class="container mt-5">

            <div class="row">

                <div data-aos="fade-right" data-aos-duration="1000" class="col-sm-12 d-flex flex-column align-items-center text-center mb-4">

                    <div style="border-radius: 200px; padding: 10px; border: 1px solid {{ $blue }}">
                        <img style="width: 200px; height: 200px; border-radius: 200px" src="{{ asset('imgs/seminars/rania.jpg') }}" alt="">
                    </div>

                    <div style="padding: 10px 20px; border-radius: 15px;background-color: {{ $blue }}; color: white; transform: translateY(-30px)">
                        {{ en_ar('Mrs. Rania Youssef', 'أ/رانيا موسي') }}
                    </div>

                    <p class="lead">
                        {{ en_ar("Technical Analyst at VI Markets", "محللة فنية في شركة ڤي آي ماركتس") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("5 years’ experience in the financial markets", "خبرة 5 سنوات في مجال اسواق المال") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("Bachelor of Commerce, Alexandria University", "بكالريوس تجارة جامعة الاسكندرية") }}
                    </p>
                    <p class="lead">
                        {{ en_ar("It’s analyzes are based on technical analysis in all its aspects.", "تعتمد في تحليلاتها على التحليل الفني بكل جوانبه") }}
                    </p>

                </div>

            </div>

        </div>

    </div>

    @include('new_seminars.partials.cover')

    @include('new_seminars.partials.seminar_topics', [
        'topics' => [
            en_ar('Practical application for phones and computers', 'ورشة وتطبيق عملي للهواتف والكمبيوتر'),
            en_ar('The Quantities', 'بيان الكمية'),
            en_ar('The Margin', 'الهامش'),
            en_ar('Drawing tools', 'ادوات الرسم'),
            en_ar('Profit and loss', 'الربح '),
            en_ar('Deal details', 'تفاصيل '),
            en_ar('The spread', 'السبريد'),
        ]
    ])

    @include('new_seminars.partials.seminar_info', [
        'date' => '2022-09-28',
        'time' => '18:00',
        'dateFormatted' => en_ar('28 Sep', '28 سبتمبر'),
        'timeFormatted' => en_ar('6:00 pm Kuwait time', '6:00 مساءاً بتوقيت الكويت'),
        'location' => 'Zoom',
    ])

    @include('new_seminars.partials.calendar')

    @include('new_seminars.partials.numbers')

    @include('new_seminars.partials.learn&train')

    @include('new_seminars.partials.slider')

    @include('new_seminars.partials.t&c', ['presenter' => $presenter])

    @include('new_seminars.partials.footer')

    @include('new_seminars.partials.scripts')

</body>

</html>
