<!doctype html>
<html lang="en" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport"
              content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
{{--        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">--}}
        <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.5.3/css/bootstrap.min.css" integrity="sha384-JvExCACAZcHNJEc7156QaHXTnQL3hQBixvj5RV5buE7vgnNEzzskDtx9NQ4p6BJe" crossorigin="anonymous">
        <title>توفير مصر</title>
    </head>
    <body>
        <div class="container">

            <div class="my-4">

                @include('partials.success')
                @include('partials.failed')
                @include('partials.validation_errors')

                <form method="post" action="{{ route('tawfeer_masr.store') }}" enctype="multipart/form-data">

                    {{ csrf_field() }}

                    <div class="form-group">
                        <label for="title">العنوان</label>
                        <input type="text" class="form-control" name="title" value="{{ old('title') }}">
                    </div>

                    <div class="form-group">
                        <label for="date">التاريخ</label>
                        <input type="text" class="form-control" name="date" value="{{ old('date') }}">
                    </div>

                    <div class="form-group">
                        <label for="images">الصور</label>
                        <input multiple type="file" class="form-control" name="images[]">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-success" type="submit">
                            حفظ
                        </button>
                    </div>

                </form>

            </div>

            <hr>

            <div class="my-2">

                @foreach(\App\Models\TawfeerMasrOffer::query()->latest()->get() as $offer)

                    <div>
                        <h2>
                            {{ $offer->title }}
                            <a id="delete-btn" class="btn btn-danger" href="{{ route('tawfeer_masr.delete', $offer->id) }}">حذف</a>
                        </h2>
                        <h3>{{ $offer->date }}</h3>
                        <div class="row">
                            @foreach($offer->images as $image)
                                <div class="col-sm-2">
                                    <img src="{{ $image }}" class="w-100" alt="">
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <hr>

                @endforeach

            </div>

        </div>

        <script>

            document.getElementById('delete-btn').onclick = function (e) {
                if (!confirm('هل انت متأكد ؟')) {
                    e.preventDefault();
                }
            }

        </script>

    </body>
</html>
