<?php

$lang = request()->route()->parameter('lang');

function en_ar ($en , $ar) {

    $lang = request()->route()->parameter('lang') ?? 'en';

    if ($lang == 'en') {
        return $en;
    }

    if ($lang == 'ar') {
        return $ar;
    }

}

?>

<!doctype html>
<html lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="https://fonts.googleapis.com/css?family=Tajawal" rel="stylesheet">


    <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.2.1/css/bootstrap.min.css" integrity="sha384-vus3nQHTD+5mpDiZ4rkEPlnkcyTP+49BhJ4wJeJunw06ZAp+wzzeBPUXr42fi8If" crossorigin="anonymous">

    <title>Live Trading Competition</title>

    <style>

        body {
            margin: 0;
            color: white;
            font-family: 'Tajawal', sans-serif;
        }

        .app-container {
            background-image: url("{{ asset('imgs/competition4_background.jpeg') }}");
            background-size: cover;
            background-repeat: no-repeat;
            height: 100vh;
            overflow-y: scroll;
        }

        @media (max-width: 786px) {

            .app-container {
                background-image: url("{{ asset('imgs/competition4_background.jpeg') }}");
            }

        }

        .form-container {
            background-color: #ffffff;
            border-radius: 4px;
            color: #000000;
            padding: 20px;
        }

        p {
            font-size: 18px;
        }

        @if($lang == 'ar')

        p {
            direction: rtl;
        }

        body {
            direction: rtl;
        }

        @endif

    </style>

    <style>

        .countdown {
            font-family: 'Roboto';
            text-transform: uppercase;
        }

        .countdown > div { display: inline-block; }

        .countdown > div > span {
            display: block;
            text-align: center;
        }

        .countdown-container { margin: 0 3px; }

        .countdown-container .countdown-heading {
            font-size: 11px;
            margin: 3px;
            color: #fff
        }

        .countdown-container .countdown-value {
            font-size: 50px;
            background: #3379b7;
            padding: 10px;
            color: #fff;
            text-shadow: 2px 2px 2px rgba(0,0,0,0.4);
            border-radius:5px;
            box-shadow: 2px 2px 2px rgba(0,0,0,0.4);
        }
    </style>

    <style type="text/css">
        td .position.pos1 {
            background-color: #8de876;
            padding: 10px;
            color: #fff;
        }
        td .position.pos2 {
            background-color: #cee05a;
            padding: 10px;
            color: #fff;
        }
        td .position.pos3 {
            background-color: #73b2c9;
            padding: 10px;
            color: #fff;
        }
    </style>

</head>

<body>

<div class="app-container">

    <div class="container">

        <div class="row">

            <div class="col-sm-12">

                <div class="text-center">
                    @include('partials.success')
                    @include('partials.validation_errors')
                </div>

            </div>

        </div>

        <div class="row mt-4">

            <div class="col-sm-12 text-center text-primary">

                <h1 style="font-size: 50px; font-weight: bold">
                    أساسيات التداول
                </h1>

            </div>

            <div class="col-sm-12 text-center text-primary mt-4">

                <img class="rounded-circle" style="width: 30%" src="{{ asset('imgs/ranim.JPG') }}" alt="">

            </div>

            <div class="col-sm-12 mt-5">

                <h1 style="font-weight: bold" class="text-white">المحاور</h1>

                <ul class="text-white list-unstyled">
                    <li>
                        <p class="lead mb-1 font-weight-bold">ما هو سوق العملات؟</p>
                    </li>
                    <li>
                        <p class="lead mb-1 font-weight-bold">
                            كيف تقرأ الاسعار في سوق صرف العملات الاجنبية؟
                        </p>
                    </li>
                    <li>
                        <p class="lead mb-1 font-weight-bold">
                            التداول عن طريق وسيط
                        </p>
                    </li>
                    <li>
                        <p class="lead mb-1 font-weight-bold">تعريف السبريد / الرافعة المالية / المارجن</p>
                    </li>
                    <li>
                        <p class="lead mb-1 font-weight-bold">التعرف على منصة MT4</p>
                    </li>
                </ul>

            </div>

            <div class="mt-4 text-center col-sm-12">

                <div class='countdown text-center' style="margin: 25px 0" data-date="2021-08-09" data-time="19:00"></div>

            </div>

        </div>

        <div class="row justify-content-center">

            <div class="mt-5 mb-5 col-sm-6">

                <div class="card">

                    <div class="card-body">

                        <form action="{{ route('trading_course.register') }}" method="post">

                            {{ csrf_field() }}

                            <div class="form-group">

                                <input type="text" class="form-control" name="name" placeholder="الأسم">

                            </div>

                            <div class="form-group">

                                <input type="text" class="form-control" name="phone" placeholder="رقم الهاتف">

                            </div>

                            <div class="form-group text-center">

                                <button class="btn btn-success" type="submit">
                                    أرسال
                                </button>

                            </div>

                        </form>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>


<script src="{{ asset('js/jquery.min.js') }}"></script>


<script>

    (function ( $ ) {
        function pad(n) {
            return (n < 10) ? ("0" + n) : n;
        }

        $.fn.showclock = function() {

            var currentDate=new Date();
            var fieldDate=$(this).data('date').split('-');
            var futureDate=new Date(fieldDate[0],fieldDate[1]-1,fieldDate[2]);
            var seconds=futureDate.getTime() / 1000 - currentDate.getTime() / 1000;

            if(seconds<=0 || isNaN(seconds)){
                this.hide();
                return this;
            }

            var days=Math.floor(seconds/86400);
            seconds=seconds%86400;

            var hours=Math.floor(seconds/3600);
            seconds=seconds%3600;

            var minutes=Math.floor(seconds/60);
            seconds=Math.floor(seconds%60);

            var html="";

            if(days!=0){
                html+="<div class='countdown-container days'>"
                html+="<span class='countdown-heading days-top'>{{ en_ar('أيام', 'أيام') }}</span>";
                html+="<span class='countdown-value days-bottom'>"+pad(days)+"</span>";
                html+="</div>";
            }

            html+="<div class='countdown-container hours'>"
            html+="<span class='countdown-heading hours-top'>{{ en_ar('ساعة', 'ساعة') }}</span>";
            html+="<span class='countdown-value hours-bottom'>"+pad(hours)+"</span>";
            html+="</div>";

            html+="<div class='countdown-container minutes'>"
            html+="<span class='countdown-heading minutes-top'>{{ en_ar('دقيقة', 'دقيقة') }}</span>";
            html+="<span class='countdown-value minutes-bottom'>"+pad(minutes)+"</span>";
            html+="</div>";

            {{--html+="<div class='countdown-container seconds'>"--}}
            {{--html+="<span class='countdown-heading seconds-top'>{{ en_ar('Seconds', 'ثواني') }}</span>";--}}
            {{--html+="<span class='countdown-value seconds-bottom'>"+pad(seconds)+"</span>";--}}
            {{--html+="</div>";--}}

            this.html(html);
        };

        $.fn.countdown = function() {
            var el=$(this);
            el.showclock();
            setInterval(function(){
                el.showclock();
            },1000);

        }

    }(jQuery));

    jQuery(document).ready(function(){
        if(jQuery(".countdown").length>0)
            jQuery(".countdown").countdown();
    });

    {{--    @if($lang == 'ar')--}}
    {{--        $(document).ready(function () {--}}
    {{--            setTimeout(function () {--}}
    {{--                $('p[class!="text-center"]').css({--}}
    {{--                    'text-align': 'right',--}}
    {{--                    'direction': 'rtl'--}}
    {{--                });--}}
    {{--            }, 1000)--}}
    {{--        });--}}
    {{--    @endif--}}

</script>

</body>

</html>
