@extends('layouts.master')

@section('title' , 'أضافة أدمن جديد')

@section('content')

    <div class="container">

        @include('partials.failed')
        @include('partials.success')
        @include('partials.validation_errors')

        <div class="row">

            <div class="col-md-6 col-md-offset-3">

                <div class="admin-login-form">

                    {{ Form::open(['route' => 'user.save']) }}

                        <div class="form-group">

                            <h4>{{ Form::label('name' , 'الأسم : ') }}</h4>
                            {{ Form::text('name' , null , ['class' => 'form-control' , 'required' => true , 'id' => 'email']) }}

                        </div>

                        <div class="form-group">

                            <h4>{{ Form::label('email' , 'البريد الألكترونى : ') }}</h4>
                            {{ Form::email('email' , null , ['class' => 'form-control' , 'required' => true , 'id' => 'email']) }}

                        </div>

                        <div class="form-group">

                            <h4>{{ Form::label('password' , 'كلمة المرور : ') }}</h4>
                            {{ Form::password('password' , ['class' => 'form-control' , 'required' => true , 'id' => 'password']) }}

                        </div>

                        <div class="form-group">

                            <h4>{{ Form::label('password_confirmation' , 'تأكيد كلمة المرور : ') }}</h4>
                            {{ Form::password('password_confirmation' , ['class' => 'form-control' , 'required' => true , 'id' => 'password']) }}

                        </div>

                        <div class="form-group">

                            {{ Form::radio('role' , 1) }}

                            {{ Form::label('role' , 'قراءة وحذف') }}

                        </div>

                        <div class="form-group">

                            {{ Form::submit('أضافة' , ['class' => 'btn btn-success']) }}

                        </div>

                    {{ Form::close() }}

                </div>

            </div>

            @if(count($users))

                <div class="clearfix"></div>

                <hr>

                <h2>الأعضاء</h2>

                <table class="table table-bordered">

                    <tr>
                        <th>الأسم</th>
                        <th>البريد الألكترونى</th>
                        <th>الصلاحية</th>
                        <th>حذف</th>
                    </tr>

                    @foreach($users as $user)

                        <tr>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->role == 1 ? 'قراءة و حذف' : 'قراءة فقط' }}</td>
                            <td>
                                <a class="btn btn-danger delete-btn" href="{{ route('auth.delete_user' , ['id' => $user->id]) }}">حذف</a>
                            </td>
                        </tr>

                    @endforeach

                </table>

            @endif

        </div>

    </div>

@endsection