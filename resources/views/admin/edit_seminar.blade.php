@extends('layouts.master')

@section('title' , 'تعديل الندوة')

@section('style')

    <style>

        #map {
            height: 300px;
        }

        #map_query {
            width: 70%;
            right: 0;
        }

        .submit-btn {
            margin-top: 50px;
        }

    </style>

@endsection

@section('content')

<div class="container">

    <div class="page-header">
        <h1>
تعديل ندوة :
{{ $seminar->title }}
        </h1>
    </div>


    {{ Form::model($seminar , ['route' => ['seminars.update' , $seminar->id] , 'files' => true , 'id' => 'create-seminar-form']) }}

        @include('admin.seminar_form')

        <div class="form-group submit-btn">

            <a href="#" id="submit-btn" class="btn btn-success btn-block">تعديل الندوة</a>

        </div>

    {{ Form::close() }}



</div>

@endsection

@section('script')

<script>


    $('.select').select2({
        tags : true ,
        dir : 'rtl'
    });

    $('#submit-btn').on('click' , function (e) {

        $('#create-seminar-form').submit();

    });

    $('#add-new-lecturer-btn').on('click' , function (e) {

        e.preventDefault();

        var countLecturers = $('input[name="image[]"]').length;

        var template = '<div class="form-group">' +
                            '<label for="name">اسم المحاضر</label>' +
                            '<input type="text" name="name[]" class="form-control"/>' +
                        '</div>' +
                        '<div class="form-group">' +
                            '<label for="description">وصف المحاضر : </label>' +
                            '<input type="text" name="description[]" class="form-control"/>' +
                        '</div>' +
                        '<div class="form-group">' +
                            '<label for="lecturer_img">صورة المحاضر : </label>' +
                            '<input type="file" name="image[]" class="form-control" />' +
                        '</div>';

        $('#lecturers-inputs').append(template);

    });

    function initAutocomplete() {
    var map = new google.maps.Map(document.getElementById('map'), {
      center: {lat: -33.8688, lng: 151.2195},
      zoom: 13,
      mapTypeId: 'roadmap'
    });

    // Create the search box and link it to the UI element.
    var input = document.getElementById('map_query');
    var searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    // Bias the SearchBox results towards current map's viewport.
    map.addListener('bounds_changed', function() {
      searchBox.setBounds(map.getBounds());
    });

    var markers = [];
    // Listen for the event fired when the user selects a prediction and retrieve
    // more details for that place.
    searchBox.addListener('places_changed', function() {
      var places = searchBox.getPlaces();

      if (places.length == 0) {
        return;
      }

      // Clear out the old markers.
      markers.forEach(function(marker) {
        marker.setMap(null);
      });
      markers = [];

      // For each place, get the icon, name and location.
      var bounds = new google.maps.LatLngBounds();
      places.forEach(function(place) {
        if (!place.geometry) {
          console.log("Returned place contains no geometry");
          return;
        }
        var icon = {
          url: place.icon,
          size: new google.maps.Size(71, 71),
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(17, 34),
          scaledSize: new google.maps.Size(25, 25)
        };

        // Create a marker for each place.
        markers.push(new google.maps.Marker({
          map: map,
          icon: icon,
          title: place.name,
          position: place.geometry.location
        }));

        if (place.geometry.viewport) {
          // Only geocodes have viewport.
          bounds.union(place.geometry.viewport);
        } else {
          bounds.extend(place.geometry.location);
        }
      });
      map.fitBounds(bounds);
    });
    }

</script>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB9DhytejaZyP3VQFuy332d0f9EM2SS3zw&libraries=places&callback=initAutocomplete"
     async defer></script>

@endsection