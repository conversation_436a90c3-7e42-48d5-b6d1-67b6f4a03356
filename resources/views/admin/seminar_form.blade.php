<div class="row">

    <div class="col-md-6">

        <div class="form-group">

            {{ Form::label('title' , 'عنوان الندوة : ') }}
            {{ Form::text('title' , null , ['class' => 'form-control']) }}

        </div>

        <div class="form-group">

            {{ Form::label('place' , 'مكان الندوة : ') }}
            {{ Form::text('place' , null , ['class' => 'form-control']) }}

        </div>
        
        <div class="form-group">
        	
        	{{ Form::label('content_header' , 'مقدمة المحتويات : ') }}
            {{ Form::text('content_header' , null , ['class' => 'form-control']) }}
        
        </div>

        <div class="form-group">

            {{ Form::label('contents' , 'محتويات الندوة  : ') }}

            @if(isset($seminar))

                {{ Form::select('contents[]' , $seminar->selectedContents , $seminar->selectedContents , ['class' => 'form-control select', 'multiple' => 'true']) }}

            @else

                {{ Form::select('contents[]' , [] , null , ['class' => 'form-control select' , 'multiple' => 'true']) }}

            @endif

        </div>

        <div class="form-group">

            {{ Form::label('date' , 'تاريخ الندوة : ') }}
            {{ Form::text('date' , null , ['class' => 'form-control']) }}

        </div>

        <div class="form-group">

            {{ Form::label('time' , 'ساعة  الندوة : ') }}
            {{ Form::text('time' , null , ['class' => 'form-control' , 'placeholder' => 'مثلا : الساعة 6 مساء']) }}

        </div>

        <div class="form-group">

            {{ Form::label('logo' , 'اللوجو : ') }}

            <input type="radio" name="logo" value="yasmeen_logo.png" {{
                isset($seminar) ?
                str_contains($seminar->logo , 'yasmeen_logo') ? 'checked' : null :
                null
             }}>

            ياسمين

            <input type="radio" name="logo" value="vimarkets_logo.png" {{
                isset($seminar) ?
                str_contains($seminar->logo , 'vimarkets_logo') ? 'checked' : null :
                null
            }}>

            VI-MARKETS

        </div>

        <div class="form-group">

            {{ Form::text('map_address' , null , ['class' => 'form-control' , 'id' => 'map_query' , 'placeholder' => 'ادخل المكان']) }}

            <div id="map"></div>

        </div>

    </div>

    <div class="col-md-6">

        <div id="lecturers-inputs">

            @if(isset($seminar))

                @if(count($seminar->lecturers))

                    @foreach($seminar->lecturers as $lecturer)

                        {{ Form::hidden('lecturer_id[]' , $lecturer->id) }}

                        <div class="form-group">

                            {{ Form::label('name' , 'اسم المحاضر : ') }}
                            {{ Form::text('name[]' , $lecturer->name , ['class' => 'form-control']) }}

                        </div>

                        <div class="form-group">

                            {{ Form::label('description' , 'وصف المحاضر : ') }}
                            {{ Form::text('description[]' , $lecturer->description , ['class' => 'form-control']) }}

                        </div>

                        @if($lecturer->image)

                            <img style="width: 50%" src="{{ asset('imgs/lecturers/' . $lecturer->image) }}">

                            <a class="btn delete-btn btn-danger" href="{{ route('delete.lecturer.image' , [$lecturer->id]) }}">حذف الصورة</a>

                        @endif

                        <div class="form-group">

                            {{ Form::label('lecturer_img' , 'صورة المحاضر : ') }}
                            {{ Form::file('image[]' , ['class' => 'form-control']) }}

                        </div>

                        <a class="btn btn-danger delete-btn" href="{{ route('delete_lecturer' , $lecturer->id) }}">حذف المحاضر</a>

                        <hr>

                    @endforeach

                @else

                    <div class="form-group">

                        {{ Form::label('name' , 'اسم المحاضر : ') }}
                        {{ Form::text('name[]' , null , ['class' => 'form-control']) }}

                    </div>

                    <div class="form-group">

                        {{ Form::label('description' , 'وصف المحاضر : ') }}
                        {{ Form::text('description[]' , null , ['class' => 'form-control']) }}

                    </div>

                    <div class="form-group">

                        {{ Form::label('lecturer_img' , 'صورة المحاضر : ') }}
                        {{ Form::file('image[]' , ['class' => 'form-control']) }}

                    </div>

                @endif

            @else

                <div class="form-group">

                    {{ Form::label('name' , 'اسم المحاضر : ') }}
                    {{ Form::text('name[]' , null , ['class' => 'form-control']) }}

                </div>

                <div class="form-group">

                    {{ Form::label('description' , 'وصف المحاضر : ') }}
                    {{ Form::text('description[]' , null , ['class' => 'form-control']) }}

                </div>

                <div class="form-group">

                    {{ Form::label('lecturer_img' , 'صورة المحاضر : ') }}
                    {{ Form::file('image[]' , ['class' => 'form-control']) }}

                </div>

            @endif

        </div>

        <a class="btn btn-info" id="add-new-lecturer-btn">
            <span class="glyphicon glyphicon-plus"></span>
            أضف محاضر أخر
        </a>

    </div>

</div>






