@extends('layouts.master')

@section('title' , 'الندوات')

@section('style')

    <style>

        .seminars-dropdown {
            margin-bottom: 30px;
        }

        .table-container {
            overflow-x: scroll;
            margin-bottom: 40px;
        }

    </style>

@endsection

@section('content')

<div class="container">

    @include('partials.success')

    <div class="page-header">

        <h1>
        الندوات
        </h1>

    </div>

    @if (count($seminars))

        <div class="dropdown seminars-dropdown">

          <button class="btn btn-info dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            أختار الندوة
            <span class="caret"></span>
          </button>

          <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">

            <li>
                <a href="{{ route('seminars') }}">------</a>
            </li>

            @foreach($seminars as $singleSeminar)

                @continue(in_array($singleSeminar->id, [69,70,71,72,73,74]))

                <li class="{{ $seminar_id == $singleSeminar->id ? 'active' : null}}">

                    <a href="{{ route('seminars' , [$singleSeminar->id]) }}">
                        {{ $singleSeminar->title  }}
                    </a>

                </li>

            @endforeach

          </ul>

        </div>

        @if ($seminar)

            <div class="alert alert-info">

                <strong>رابط الندوة : </strong>

                <a href="{{ $seminar->url }}">
                    {{ $seminar->url }}
                </a>

            </div>

            <div class="table-container">

                <table class="table table-bordered">

                    <tr>
                        <th>
                        عنوان الندوة
                        </th>
                        <th>
                        مكان الندوة
                        </th>
                        <th>
                        تاريخ الندوة
                        </th>
                        <th>
                        وقت الندوة
                        </th>
                        <th>
                        محتويات الندوة
                        </th>

                        @if(request()->user()->role)

                            <th>
                                تعديل
                            </th>
                            <th>
                                حذف
                            </th>

                        @endif

                    </tr>

                    <tr>
                        <td>
                        {{ $seminar->title }}
                        </td>
                        <td>
                        {{ $seminar->place }}
                        </td>
                        <td>
                        {{ $seminar->date }}
                        </td>
                        <td>
                        {{ $seminar->time }}
                        </td>
                        <td>
                            <ul>
                                @foreach($seminar->contents as $content)
                                   <li>{{ $content }}</li>
                                @endforeach
                            </ul>
                        </td>

                        @if(request()->user()->role)

                            <td>
                                <a href="{{ route('seminars.edit' , [$seminar->id]) }}" class="btn btn-info">تعديل</a>
                            </td>
                            <td>
                                <a href="{{ route('seminars.delete' , [$seminar->id]) }}" class="btn btn-danger delete-btn">حذف</a>
                            </td>

                        @endif

                    </tr>

                </table>

            </div>

            @if(count($seminar->participants))

                <div class="page-header">
                    <h3>
                        المشتركين بالندوة
                        <a class="btn btn-success" href="{{ route('excel.export' , ['participant' , $seminar->id]) }}">
                            <span class="glyphicon glyphicon-search" aria-hidden="true"></span>
                            ملف الأكسيل
                        </a>
                    </h3>
                </div>

                <div class="table-container">

                    <table class="table table-bordered">

                        <tr>
                            <th>
                            الرقم
                            </th>
                            <th>
                                الأسم الأول
                            </th>

                            <th>
    الأسم الأخير
                            </th>
                            <th>
                            الدولة
                            </th>
                            <th>
    البريد الألكترونى
                            </th>
                            <th>
    رقم الهاتف
                            </th>
                            <th>
                                الندوة
                            </th>
                        </tr>

                        <?php $counter = 1 ?>

                        @foreach($seminar->participants as $participant)

                            <tr>
                                <td>
                                    {{ $counter++ }}
                                </td>
                                <td>
                                    {{$participant->first_name}}
                                </td>
                                <td>
                                    {{$participant->last_name}}
                                </td>
                                <td>
                                    {{$participant->country}}
                                </td>
                                <td>
                                    {{$participant->email}}
                                </td>
                                <td>
                                    {{$participant->phone}}
                                </td>
                                <td>
                                    {{ $participant->seminar }}
                                </td>
                            </tr>

                        @endforeach

                    </table>

                </div>

            @endif

        @endif

    @else

        <h2>لا يوجد ندوات حاليا</h2>

    @endif

</div>

@endsection