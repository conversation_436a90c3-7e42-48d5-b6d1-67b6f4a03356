<!DOCTYPE html>
<html lang="en">

  <head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title')</title>

    <link href="{{ asset('css/bootstrap.css') }}" rel="stylesheet">
    <link href="{{ asset('css/bootstrap-rtl.css') }}" rel="stylesheet">
    <link href="{{ asset('css/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/style.css') }}" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Cairo" rel="stylesheet">

    <link rel="shortcut icon" type="image/vnd.microsoft.icon" href="{{ asset('imgs/favicon_vimarkets_25px.png') }}">

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    @yield('style')

  </head>

  <body>


<nav class="navbar navbar-inverse navbar-fixed-top my-navbar">

    <div class="container-fluid">

    <div class="navbar-header">

        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        </button>

        <a class="navbar-brand" href="{{ request()->is('admin') || request()->is('admin/*') ? route('admin.get.login') : '#' }}">VI Markets</a>

    </div>

    <!-- Collect the nav links, forms, and other content for toggling -->
    <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">

        <ul class="nav navbar-nav">

            @if (!request()->is('admin') && !request()->is('admin/*'))

                <li class="active"><a href="#home-page">الصفحة الرئيسية</a> </li>

                <li><a href="#register">التسجيل</a></li>

                <li><a href="#lecturers">المحاضر</a> </li>

                <li><a href="#map">الخريطة</a> </li>

            @elseif (auth()->user())

               <li>
                <a href="{{ route('seminars') }}">
الندوات
                </a>
               </li>

                @if(request()->user()->role)

                    <li>
                        <a href="{{ route('seminars.create') }}">
                            أضافة ندوة جديدة
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('user.create') }}">
                            أضافة أدمن جديد
                        </a>
                    </li>

                @endif

            @endif

        </ul>

        @if(auth()->user())

            <ul class="nav navbar-nav navbar-right">
              <li><a href="{{ route('logout') }}">تسجيل الخروج</a></li>
            </ul>

        @endif

        </div><!-- /.navbar-collapse -->

    </div><!-- /.container-fluid -->

</nav>

    @yield('content')

    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('js/tether.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('js/select/select2.full.min.js') }}"></script>
    <script src="{{ asset('js/select/select2.min.js') }}"></script>
    <script src="{{ asset('js/select/ar.js') }}"></script>

    @yield('script')

    <script>

        $('.delete-btn').on('click' , function (e) {

            if (!confirm(' هل تريد الحذف ?')) {
                e.preventDefault();
            }

        });

    </script>

  </body>

</html>