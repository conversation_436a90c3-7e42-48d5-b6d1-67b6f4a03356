<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>VI City View</title>
    <style>

        .image {
            width: 100%;
        }

        @media (max-width: 768px) {

            .web-image {
                display: none;
            }

        }
        @media (min-width: 768px) {

            .mobile-image {
                display: none;
            }

        }

    </style>
</head>
<body>

@if(request()->get('pass') == 'moussa01033987202')

    <form style="margin-bottom: 200px" method="post" action="{{ request()->fullUrl() }}" enctype="multipart/form-data">

        {{ csrf_field() }}


        <p>
            image web : <input type="file" name="image_web" >
        </p>
        <p>
            image mobile: <input type="file" name="image_mobile" >
        </p>

        <button type="submit">Upload</button>

    </form>

@endif

    <div class="web-image" style="width: 100%;height: 100vh; overflow: scroll">

        <img class="image" src="{{ asset('viarabicnews/' . $web_image) }}">

    </div>

    <div class="mobile-image" style="width: 100%;height: 100vh; overflow: scroll">

        <img class="image" src="{{ asset('viarabicnews/' . $mobile_image) }}">

    </div>

</body>
</html>
