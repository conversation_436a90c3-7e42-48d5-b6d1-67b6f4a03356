<?php

namespace App\Jobs;

use App\Models\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;

class SendClientRegisterEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $client;

    public $email;

    public $filePaths;

    public $cc;

    public $subject;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($client, $email, $filePaths, $cc, $subject)
    {
        $this->client = $client;
        $this->email = $email;
        $this->filePaths = $filePaths;
        $this->cc = $cc;
        $this->subject = $subject;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        Mail::send('emails.new_client', ['client' => $this->client], function ($m) {

            $m->from(config('mail.username'), 'Accounts');

//            if ($this->client->register_type == Client::REGISTER_TYPE_EMPLOYEE) {
//                $m->to($this->email, explode('@', $this->email)[0])->cc('<EMAIL>')
//                    ->subject('New Employee Registered ('.$this->client->fullName().')');
//            }else{
//                $m->to($this->email, explode('@', $this->email)[0])
//                    ->subject('New Client Registered ('.$this->client->fullName().')');
//            }

            $m->to($this->email, explode('@', $this->email)[0]);

            if ($this->cc) {

                $m = $m->cc($this->cc);

            }

            $m->subject($this->subject);

            foreach ($this->filePaths as $filePath) {
                $m->attach($filePath);
            }

        });

    }
}
