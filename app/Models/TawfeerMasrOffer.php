<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use phpDocumentor\Reflection\Types\Collection;

class TawfeerMasrOffer extends Model
{

    protected $fillable = [
        'title',
        'date',
        'images'
    ];

    public function setImagesAttribute ($value)
    {

        $value = $value ?? [];

        $savedFiles = [];

        foreach ($value as $file) {
            /** @var UploadedFile $file */

            $savedFiles[] = $file->store('tawfeer_masr', 'public_storage');

        }

        $this->attributes['images'] = json_encode($savedFiles);

    }

    public function getImagesAttribute ($value)
    {

        $value = json_decode($value, true);

        if (count($value)) {
            return array_map(function ($path) {
                return Storage::disk('public_storage')->url($path);
            }, $value);
        }

        return [];

    }

}
