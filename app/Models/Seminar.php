<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Seminar extends Model
{

    protected $fillable = ['title' , 'place' , 'date' , 'time' , 'contents' , 'map_address' , 'url' , 'logo' , 'content_header'];

    public function lecturers () {

        return $this->hasMany('App\Models\Lecturer');

    }

    public function participants () {

        return $this->hasMany('App\Models\Participant');

    }

    public function getContentsAttribute ($value) {
        return ($value) ? explode(',' , $value) : [];
    }

    public function getSelectedContentsAttribute () {

        $contents = [];

        foreach ($this->contents as $key => $value) {
            $contents[$value] = $value;
        }

        return $contents;

    }

    public function getLogoUrlAttribute () {

        return $this->logo ? asset('imgs/' . $this->logo) : null;

    }

    public function isLogo ($needle) {
        return str_contains($this->logo , $needle);
    }

}
