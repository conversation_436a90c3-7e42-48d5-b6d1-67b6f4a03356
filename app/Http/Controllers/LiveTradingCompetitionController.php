<?php

namespace App\Http\Controllers;

use App\Models\LiveTradingCompetition;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LiveTradingCompetitionController extends Controller
{

    public function index () {

        return view('live_trading_competition');

    }

    public function index2 () {

        return view('live_trading_competition2');

    }

    public function index3 () {

        return view('live_trading_competition3');

    }

    public function index4 () {

        abort(404);

        return view('live_trading_competition4');

    }

    public function index5 () {

        return view('live_trading_competition5');

    }

    public function termsAndConditions () {

        return response()
            ->make(Storage::disk('public_storage')->get('VI Markets Live Trading Competition – September 2020.pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function termsAndConditions3 () {

        return response()
            ->make(Storage::disk('public_storage')->get('VI Markets Live Trading Competition – September 2020.pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function termsAndConditions4 () {

        return response()
            ->make(Storage::disk('public_storage')->get('T&C.pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function termsAndConditions5 () {

        return response()
            ->make(Storage::disk('public_storage')->get('VI Markets Live Trading Competition –June 2021.pdf'))
            ->withHeaders([
                'Content-Type' => 'application/pdf'
            ]);

    }

    public function join (Request $request) {

        $this->validate($request, [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'mt4_number' => 'required'
        ]);

        LiveTradingCompetition::create($request->all());

        return redirect()->back()
            ->with('success', 'تم الأشتراك بنجاح');

    }

    public function participants () {

        $participants = LiveTradingCompetition::whereDate('created_at', '>=', Carbon::parse('2021-05-16'))->get();

        return view('live_trading_competition_participants', compact('participants'));

    }

}
