<?php

namespace App\Http\Controllers\Admin;

use App\Models\Lecturer;
use App\Models\Seminar;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use Route;

class SeminarController extends Controller
{

    protected $seminar;
    protected $lecturer;

    public function __construct (Seminar $seminar, Lecturer $lecturer) {

        $this->seminar = $seminar;
        $this->lecturer = $lecturer;

    }

    public function seminarsWithParticipants ($seminar_id = null) {

        $seminars = $this->seminar
            ->latest('created_at')
            ->select(['id' , 'title'])
            ->get();

        $seminar = null;

        if ($seminar_id) {
            $seminar = $this->seminar
                ->with(['participants' , 'lecturers'])
                ->findOrFail($seminar_id);
        }

        return view('admin.seminars' , compact('seminars' , 'seminar' , 'seminar_id'));

    }

    public function createSeminar () {

        return view('admin.create_seminar');

    }

    public function saveSeminar (Request $request) {

        $seminar = $this->seminar->create([
            'title' => $request->input('title') ?: null,
            'place' => $request->input('place') ?: null,
            'date' => $request->input('date') ?: null,
            'time' => $request->input('time') ?: null,
            'content_header' => $request->input('content_header') ?: null,
            'contents' => $request->input('contents') ? implode(',' , $request->input('contents')) : null,
            'map_address' => $request->input('map_address') ?: null,
            'logo' => $request->input('logo') ?: 'vimarkets_logo.png',
        ]);

        $seminar->url = route('seminar' , [$seminar->id]);
        $seminar->save();

        $lecturers_names = $request->input('name');
        $lecturers_descriptions = $request->input('description');
        $lecturers_images = $request->file('image');

        for ($i = 0 ; $i < count($lecturers_names) ; $i++) {

            $seminar->lecturers()->create([
                'name' => $lecturers_names[$i] ?: null ,
                'description' => $lecturers_descriptions[$i] ?: null ,
                'image' => $lecturers_images[$i] ? $this->uploadLecturerImage($lecturers_images[$i]) : null
            ]);

        }

        return redirect()->route('seminars' , [$seminar->id])
            ->with('success' , 'تم أضافة الندوة بنجاح');

    }

    public function editSeminar ($seminar_id) {

        $seminar = $this->seminar->with(['lecturers'])->findOrFail($seminar_id);

        return view('admin.edit_seminar' , compact('seminar'));

    }

    public function updateSeminar (Request $request , $seminar_id) {

        $seminar = $this->seminar->with(['lecturers'])->findOrFail($seminar_id);

        $seminar->title = $request->input('title') ?: null;
        $seminar->place = $request->input('place') ?: null;
        $seminar->date = $request->input('date') ?: null;
        $seminar->time = $request->input('time') ?: null;
        $seminar->content_header= $request->input('content_header') ?: null;
        $seminar->contents = $request->input('contents') ? implode(',' , $request->input('contents')) : null;
        $seminar->map_address = $request->input('map_address') ?: null;
        $seminar->logo = $request->input('logo') ?: 'vimarkets_logo.png';

        $seminar->save();

        $lecturers_ids = $request->input('lecturer_id');
        $lecturers_names = $request->input('name');
        $lecturers_descriptions = $request->input('description');
        $lecturers_images = $request->file('image');

        for ($i = 0; $i < count($lecturers_names) ; $i++) {

            if (isset($lecturers_ids[$i])) {

                $lecturer = $this->lecturer->findOrFail($lecturers_ids[$i]);

                $lecturer->name = $lecturers_names[$i];
                $lecturer->description = $lecturers_descriptions[$i];
                $lecturer->image = isset($lecturers_images[$i]) ? $this->uploadLecturerImage($lecturers_images[$i]) : $lecturer->image;

                $lecturer->save();

            }else{

                $seminar->lecturers()->create([
                    'name' => $lecturers_names[$i] ?: null ,
                    'description' => $lecturers_descriptions[$i] ?: null ,
                    'image' => $lecturers_images[$i] ? $this->uploadLecturerImage($lecturers_images[$i]) : null
                ]);

            }

        }

        return redirect()->route('seminars' , [$seminar->id])
                ->with('success' , 'تم تعديل الندوة بنجاح');

    }

    private function uploadLecturerImage ($lecturers_image) {

        $image_name = uniqid() .
            Carbon::now()->timestamp .
            '.' .
            $lecturers_image->getClientOriginalExtension();

        $lecturers_image->move(public_path('imgs/lecturers') , $image_name);

        return $image_name;

    }

    public function deleteLecturerImage($lecturer_id) {

        $lecturer = $this->lecturer->findOrFail($lecturer_id);

        unlink(public_path('imgs/lecturers/' . $lecturer->image));

        $lecturer->image = null;

        $lecturer->save();

        return redirect()->back();
    }

    public function deleteLecturer ($lecturer_id) {

        $this->lecturer->findOrFail($lecturer_id)->delete();

        return redirect()->back();

    }

    public function deleteSeminar ($seminar_id) {

        $this->seminar->findOrFail($seminar_id)->delete();

        return redirect()->route('seminars');

    }

}

