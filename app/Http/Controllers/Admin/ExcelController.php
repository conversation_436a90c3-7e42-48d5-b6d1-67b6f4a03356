<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use Excel;

class ExcelController extends Controller
{

    public function export ($model , $seminar_id)
    {

        $app_model = 'App\Models\\' . ucfirst($model);

        $all = (new $app_model)->where('seminar_id' , $seminar_id)->get()->toArray();

        Excel::create($model . 's' , function($excel) use ($all) {

            $excel->sheet('Sheetname', function($sheet) use ($all) {

                $sheet->fromArray($all);

            });

        })->download('xls');

    }

}
