<?php

namespace App\Http\Controllers\Website;

use App\Models\News;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class NewsController extends Controller
{

    protected $news;

    public function __construct(News $news)
    {
        $this->news = $news;
    }

    public function newsFeed () {

        $news = $this->news->latest()->paginate(20);

        return view('website.news.news_feed' , compact('news'));

    }

    public function newsItem ($id , $title = null) {

        $newsItem = $this->news->findOrFail($id);

        return view('website.news.news_item' , compact('newsItem'));

    }


}
