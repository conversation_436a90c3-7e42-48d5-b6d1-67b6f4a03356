<?php

namespace App\Http\Controllers\Website;

use Illuminate\Filesystem\Filesystem;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LearnWithUsController extends Controller
{

    public function index () {

        $presentations = [
            [
                'title' => 'الفصل الأول: عالم أسواق المال',
                'filename' => 'chapter1'
            ],
            [
                'title' => 'الفصل الثاني: الفوركس، معاني و أسرار',
                'filename' => 'chapter3'
            ],
            [
                'title' => 'الفصل الثالث: العقود مقابل الفروقات (مزايا و مخاطر)',
                'filename' => 'chapter2'
            ],
            [
                'title' => 'الفصل الرابع: صناديق الأستثمار في التداول',
                'filename' => 'chapter4'
            ],
            [
                'title' => 'الفصل الخامس: آلية عمل السوق، حركته و مؤثرات الأسعار',
                'filename' => 'chapter5'
            ],
            [
                'title' => 'الفصل السادس: سيكولوجية التداول',
                'filename' => 'chapter6'
            ],
        ];

        return view('website.learn_with_us.index', compact('presentations'));

    }

    public function presentation (Filesystem $filesystem, $name = null) {

        return response(
            $filesystem->get(public_path('pdf/presentations/'.$name.'.pdf')),
            200,
            [
                'Content-Type' => 'application/pdf'
            ]
        );

    }

    public function images (Filesystem $filesystem, $chapter = null) {

        $files = collect($filesystem->files(public_path('storage/learn_with_us/' . $chapter)))->map(function ($file) {

            return $file->getBasename();

        })->sortBy(function ($filename) {

            return (int) $filename;

        })->map(function ($filename) use ($chapter) {

            return asset('storage/learn_with_us/'.$chapter.'/' . $filename);

        })->values()->toArray();

        return $files;

    }

}
