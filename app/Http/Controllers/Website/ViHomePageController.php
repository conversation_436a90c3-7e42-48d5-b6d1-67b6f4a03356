<?php

namespace App\Http\Controllers\Website;

use App\Models\ContactUsMessage;
use App\Models\ViHomeUsers;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ViHomePageController extends Controller
{

    public function index ($lang = 'ar') {

        return view('website.vi_homepage');

    }
    
    public function index2 ($lang = 'ar') {

        return view('website.vi_homepage2');

    }


    public function postContactUsForm (Request $request , ContactUsMessage $contactUsMessage) {

        $this->validate($request , [
            'name' => 'required' ,
            'phone' => 'required' ,
//            'email' => 'required' ,
        ]);

        $contactUsMessage->create($request->all());

        return redirect()->back()->with('success' ,
            $request->input('lang') == 'en' ? 'Thank you we will contact you soon' : 'شكرا لك وسيتم التواصل معك قريبا'
        );

    }

    public function contactUsMessages (ContactUsMessage $contactUsMessage , $form_id = 1) {

        $messages = $contactUsMessage->where('form_id' , $form_id)->latest()->get();

        return view('website.contact_us_messages' , compact('messages'));

    }

}
