<?php

namespace App\Http\Controllers\Website;

use App\Models\Seminar;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;


class HomeController extends Controller
{

    protected $seminar;

    public function __construct (Seminar $seminar) {

        $this->seminar = $seminar;

    }

    public function index ()
    {

        $countries = array( " Afghanistan " => " Afghanistan " , " Albania " => " Albania " , " Algeria " => " Algeria " , " American Samoa " => " American Samoa " , " Andorra " => " Andorra " , " Angola " => " Angola " , " Anguilla " => " Anguilla " , " Antarctica " => " Antarctica " , " Antigua and Barbuda " => " Antigua and Barbuda " , " Argentina " => " Argentina " , " Armenia " => " Armenia " , " Aruba " => " Aruba " , " Australia " => " Australia " , " Austria " => " Austria " , " Azerbaijan " => " Azerbaijan " , " Bahamas " => " Bahamas " , " Bahrain " => " Bahrain " , " Bangladesh " => " Bangladesh " , " Barbados " => " Barbados " , " Belarus " => " Belarus " , " Belgium " => " Belgium " , " Belize " => " Belize " , " Benin " => " Benin " , " Bermuda " => " Bermuda " , " Bhutan " => " Bhutan " , " Bolivia " => " Bolivia " , " Bosnia and Herzegovina " => " Bosnia and Herzegovina " , " Botswana " => " Botswana " , " Bouvet Island " => " Bouvet Island " , " Brazil " => " Brazil " , " British Antarctic Territory " => " British Antarctic Territory " , " British Indian Ocean Territory " => " British Indian Ocean Territory " , " British Virgin Islands " => " British Virgin Islands " , " Brunei " => " Brunei " , " Bulgaria " => " Bulgaria " , " Burkina Faso " => " Burkina Faso " , " Burundi " => " Burundi " , " Cambodia " => " Cambodia " , " Cameroon " => " Cameroon " , " Canada " => " Canada " , " Canton and Enderbury Islands " => " Canton and Enderbury Islands " , " Cape Verde " => " Cape Verde " , " Cayman Islands " => " Cayman Islands " , " Central African Republic " => " Central African Republic " , " Chad " => " Chad " , " Chile " => " Chile " , " China " => " China " , " Christmas Island " => " Christmas Island " , " Cocos [Keeling] Islands " => " Cocos [Keeling] Islands " , " Colombia " => " Colombia " , " Comoros " => " Comoros " , " Congo - Brazzaville " => " Congo - Brazzaville " , " Congo - Kinshasa " => " Congo - Kinshasa " , " Cook Islands " => " Cook Islands " , " Costa Rica " => " Costa Rica " , " Croatia " => " Croatia " , " Cuba " => " Cuba " , " Cyprus " => " Cyprus " , " Czech Republic " => " Czech Republic " , " Côte d’Ivoire " => " Côte d’Ivoire " , " Denmark " => " Denmark " , " Djibouti " => " Djibouti " , " Dominica " => " Dominica " , " Dominican Republic " => " Dominican Republic " , " Dronning Maud Land " => " Dronning Maud Land " , " East Germany " => " East Germany " , " Ecuador " => " Ecuador " , " Egypt " => " Egypt " , " El Salvador " => " El Salvador " , " Equatorial Guinea " => " Equatorial Guinea " , " Eritrea " => " Eritrea " , " Estonia " => " Estonia " , " Ethiopia " => " Ethiopia " , " Falkland Islands " => " Falkland Islands " , " Faroe Islands " => " Faroe Islands " , " Fiji " => " Fiji " , " Finland " => " Finland " , " France " => " France " , " French Guiana " => " French Guiana " , " French Polynesia " => " French Polynesia " , " French Southern Territories " => " French Southern Territories " , " French Southern and Antarctic Territories " => " French Southern and Antarctic Territories " , " Gabon " => " Gabon " , " Gambia " => " Gambia " , " Georgia " => " Georgia " , " Germany " => " Germany " , " Ghana " => " Ghana " , " Gibraltar " => " Gibraltar " , " Greece " => " Greece " , " Greenland " => " Greenland " , " Grenada " => " Grenada " , " Guadeloupe " => " Guadeloupe " , " Guam " => " Guam " , " Guatemala " => " Guatemala " , " Guernsey " => " Guernsey " , " Guinea " => " Guinea " , " Guinea-Bissau " => " Guinea-Bissau " , " Guyana " => " Guyana " , " Haiti " => " Haiti " , " Heard Island and McDonald Islands " => " Heard Island and McDonald Islands " , " Honduras " => " Honduras " , " Hong Kong SAR China " => " Hong Kong SAR China " , " Hungary " => " Hungary " , " Iceland " => " Iceland " , " India " => " India " , " Indonesia " => " Indonesia " , " Iran " => " Iran " , " Iraq " => " Iraq " , " Ireland " => " Ireland " , " Isle of Man " => " Isle of Man " , " Israel " => " Israel " , " Italy " => " Italy " , " Jamaica " => " Jamaica " , " Japan " => " Japan " , " Jersey " => " Jersey " , " Johnston Island " => " Johnston Island " , " Jordan " => " Jordan " , " Kazakhstan " => " Kazakhstan " , " Kenya " => " Kenya " , " Kiribati " => " Kiribati " , " Kuwait " => " Kuwait " , " Kyrgyzstan " => " Kyrgyzstan " , " Laos " => " Laos " , " Latvia " => " Latvia " , " Lebanon " => " Lebanon " , " Lesotho " => " Lesotho " , " Liberia " => " Liberia " , " Libya " => " Libya " , " Liechtenstein " => " Liechtenstein " , " Lithuania " => " Lithuania " , " Luxembourg " => " Luxembourg " , " Macau SAR China " => " Macau SAR China " , " Macedonia " => " Macedonia " , " Madagascar " => " Madagascar " , " Malawi " => " Malawi " , " Malaysia " => " Malaysia " , " Maldives " => " Maldives " , " Mali " => " Mali " , " Malta " => " Malta " , " Marshall Islands " => " Marshall Islands " , " Martinique " => " Martinique " , " Mauritania " => " Mauritania " , " Mauritius " => " Mauritius " , " Mayotte " => " Mayotte " , " Metropolitan France " => " Metropolitan France " , " Mexico " => " Mexico " , " Micronesia " => " Micronesia " , " Midway Islands " => " Midway Islands " , " Moldova " => " Moldova " , " Monaco " => " Monaco " , " Mongolia " => " Mongolia " , " Montenegro " => " Montenegro " , " Montserrat " => " Montserrat " , " Morocco " => " Morocco " , " Mozambique " => " Mozambique " , " Myanmar [Burma] " => " Myanmar [Burma] " , " Namibia " => " Namibia " , " Nauru " => " Nauru " , " Nepal " => " Nepal " , " Netherlands " => " Netherlands " , " Netherlands Antilles " => " Netherlands Antilles " , " Neutral Zone " => " Neutral Zone " , " New Caledonia " => " New Caledonia " , " New Zealand " => " New Zealand " , " Nicaragua " => " Nicaragua " , " Niger " => " Niger " , " Nigeria " => " Nigeria " , " Niue " => " Niue " , " Norfolk Island " => " Norfolk Island " , " North Korea " => " North Korea " , " North Vietnam " => " North Vietnam " , " Northern Mariana Islands " => " Northern Mariana Islands " , " Norway " => " Norway " , " Oman " => " Oman " , " Pacific Islands Trust Territory " => " Pacific Islands Trust Territory " , " Pakistan " => " Pakistan " , " Palau " => " Palau " , " Palestinian Territories " => " Palestinian Territories " , " Panama " => " Panama " , " Panama Canal Zone " => " Panama Canal Zone " , " Papua New Guinea " => " Papua New Guinea " , " Paraguay " => " Paraguay " , " People's Democratic Republic of Yemen " => " People's Democratic Republic of Yemen " , " Peru " => " Peru " , " Philippines " => " Philippines " , " Pitcairn Islands " => " Pitcairn Islands " , " Poland " => " Poland " , " Portugal " => " Portugal " , " Puerto Rico " => " Puerto Rico " , " Qatar " => " Qatar " , " Romania " => " Romania " , " Russia " => " Russia " , " Rwanda " => " Rwanda " , " Réunion " => " Réunion " , " Saint Barthélemy " => " Saint Barthélemy " , " Saint Helena " => " Saint Helena " , " Saint Kitts and Nevis " => " Saint Kitts and Nevis " , " Saint Lucia " => " Saint Lucia " , " Saint Martin " => " Saint Martin " , " Saint Pierre and Miquelon " => " Saint Pierre and Miquelon " , " Saint Vincent and the Grenadines " => " Saint Vincent and the Grenadines " , " Samoa " => " Samoa " , " San Marino " => " San Marino " , " Saudi Arabia " => " Saudi Arabia " , " Senegal " => " Senegal " , " Serbia " => " Serbia " , " Serbia and Montenegro " => " Serbia and Montenegro " , " Seychelles " => " Seychelles " , " Sierra Leone " => " Sierra Leone " , " Singapore " => " Singapore " , " Slovakia " => " Slovakia " , " Slovenia " => " Slovenia " , " Solomon Islands " => " Solomon Islands " , " Somalia " => " Somalia " , " South Africa " => " South Africa " , " South Georgia and the South Sandwich Islands " => " South Georgia and the South Sandwich Islands " , " South Korea " => " South Korea " , " Spain " => " Spain " , " Sri Lanka " => " Sri Lanka " , " Sudan " => " Sudan " , " Suriname " => " Suriname " , " Svalbard and Jan Mayen " => " Svalbard and Jan Mayen " , " Swaziland " => " Swaziland " , " Sweden " => " Sweden " , " Switzerland " => " Switzerland " , " Syria " => " Syria " , " São Tomé and Príncipe " => " São Tomé and Príncipe " , " Taiwan " => " Taiwan " , " Tajikistan " => " Tajikistan " , " Tanzania " => " Tanzania " , " Thailand " => " Thailand " , " Timor-Leste " => " Timor-Leste " , " Togo " => " Togo " , " Tokelau " => " Tokelau " , " Tonga " => " Tonga " , " Trinidad and Tobago " => " Trinidad and Tobago " , " Tunisia " => " Tunisia " , " Turkey " => " Turkey " , " Turkmenistan " => " Turkmenistan " , " Turks and Caicos Islands " => " Turks and Caicos Islands " , " Tuvalu " => " Tuvalu " , " U.S. Minor Outlying Islands " => " U.S. Minor Outlying Islands " , " U.S. Miscellaneous Pacific Islands " => " U.S. Miscellaneous Pacific Islands " , " U.S. Virgin Islands " => " U.S. Virgin Islands " , " Uganda " => " Uganda " , " Ukraine " => " Ukraine " , " Union of Soviet Socialist Republics " => " Union of Soviet Socialist Republics " , " United Arab Emirates " => " United Arab Emirates " , " United Kingdom " => " United Kingdom " , " United States " => " United States " , " Unknown or Invalid Region " => " Unknown or Invalid Region " , " Uruguay " => " Uruguay " , " Uzbekistan " => " Uzbekistan " , " Vanuatu " => " Vanuatu " , " Vatican City " => " Vatican City " , " Venezuela " => " Venezuela " , " Vietnam " => " Vietnam " , " Wake Island " => " Wake Island " , " Wallis and Futuna " => " Wallis and Futuna " , " Western Sahara " => " Western Sahara " , " Yemen " => " Yemen " , " Zambia " => " Zambia " , " Zimbabwe " => " Zimbabwe " , " Åland Islands " => " Åland Islands " , );

        return view('website.home' , compact('countries'));

    }

    public function seminar ($seminar_id) {

        $seminar = $this->seminar->findOrFail($seminar_id);

//        $countries = array( " Afghanistan " => " Afghanistan " , " Albania " => " Albania " , " Algeria " => " Algeria " , " American Samoa " => " American Samoa " , " Andorra " => " Andorra " , " Angola " => " Angola " , " Anguilla " => " Anguilla " , " Antarctica " => " Antarctica " , " Antigua and Barbuda " => " Antigua and Barbuda " , " Argentina " => " Argentina " , " Armenia " => " Armenia " , " Aruba " => " Aruba " , " Australia " => " Australia " , " Austria " => " Austria " , " Azerbaijan " => " Azerbaijan " , " Bahamas " => " Bahamas " , " Bahrain " => " Bahrain " , " Bangladesh " => " Bangladesh " , " Barbados " => " Barbados " , " Belarus " => " Belarus " , " Belgium " => " Belgium " , " Belize " => " Belize " , " Benin " => " Benin " , " Bermuda " => " Bermuda " , " Bhutan " => " Bhutan " , " Bolivia " => " Bolivia " , " Bosnia and Herzegovina " => " Bosnia and Herzegovina " , " Botswana " => " Botswana " , " Bouvet Island " => " Bouvet Island " , " Brazil " => " Brazil " , " British Antarctic Territory " => " British Antarctic Territory " , " British Indian Ocean Territory " => " British Indian Ocean Territory " , " British Virgin Islands " => " British Virgin Islands " , " Brunei " => " Brunei " , " Bulgaria " => " Bulgaria " , " Burkina Faso " => " Burkina Faso " , " Burundi " => " Burundi " , " Cambodia " => " Cambodia " , " Cameroon " => " Cameroon " , " Canada " => " Canada " , " Canton and Enderbury Islands " => " Canton and Enderbury Islands " , " Cape Verde " => " Cape Verde " , " Cayman Islands " => " Cayman Islands " , " Central African Republic " => " Central African Republic " , " Chad " => " Chad " , " Chile " => " Chile " , " China " => " China " , " Christmas Island " => " Christmas Island " , " Cocos [Keeling] Islands " => " Cocos [Keeling] Islands " , " Colombia " => " Colombia " , " Comoros " => " Comoros " , " Congo - Brazzaville " => " Congo - Brazzaville " , " Congo - Kinshasa " => " Congo - Kinshasa " , " Cook Islands " => " Cook Islands " , " Costa Rica " => " Costa Rica " , " Croatia " => " Croatia " , " Cuba " => " Cuba " , " Cyprus " => " Cyprus " , " Czech Republic " => " Czech Republic " , " Côte d’Ivoire " => " Côte d’Ivoire " , " Denmark " => " Denmark " , " Djibouti " => " Djibouti " , " Dominica " => " Dominica " , " Dominican Republic " => " Dominican Republic " , " Dronning Maud Land " => " Dronning Maud Land " , " East Germany " => " East Germany " , " Ecuador " => " Ecuador " , " Egypt " => " Egypt " , " El Salvador " => " El Salvador " , " Equatorial Guinea " => " Equatorial Guinea " , " Eritrea " => " Eritrea " , " Estonia " => " Estonia " , " Ethiopia " => " Ethiopia " , " Falkland Islands " => " Falkland Islands " , " Faroe Islands " => " Faroe Islands " , " Fiji " => " Fiji " , " Finland " => " Finland " , " France " => " France " , " French Guiana " => " French Guiana " , " French Polynesia " => " French Polynesia " , " French Southern Territories " => " French Southern Territories " , " French Southern and Antarctic Territories " => " French Southern and Antarctic Territories " , " Gabon " => " Gabon " , " Gambia " => " Gambia " , " Georgia " => " Georgia " , " Germany " => " Germany " , " Ghana " => " Ghana " , " Gibraltar " => " Gibraltar " , " Greece " => " Greece " , " Greenland " => " Greenland " , " Grenada " => " Grenada " , " Guadeloupe " => " Guadeloupe " , " Guam " => " Guam " , " Guatemala " => " Guatemala " , " Guernsey " => " Guernsey " , " Guinea " => " Guinea " , " Guinea-Bissau " => " Guinea-Bissau " , " Guyana " => " Guyana " , " Haiti " => " Haiti " , " Heard Island and McDonald Islands " => " Heard Island and McDonald Islands " , " Honduras " => " Honduras " , " Hong Kong SAR China " => " Hong Kong SAR China " , " Hungary " => " Hungary " , " Iceland " => " Iceland " , " India " => " India " , " Indonesia " => " Indonesia " , " Iran " => " Iran " , " Iraq " => " Iraq " , " Ireland " => " Ireland " , " Isle of Man " => " Isle of Man " , " Israel " => " Israel " , " Italy " => " Italy " , " Jamaica " => " Jamaica " , " Japan " => " Japan " , " Jersey " => " Jersey " , " Johnston Island " => " Johnston Island " , " Jordan " => " Jordan " , " Kazakhstan " => " Kazakhstan " , " Kenya " => " Kenya " , " Kiribati " => " Kiribati " , " Kuwait " => " Kuwait " , " Kyrgyzstan " => " Kyrgyzstan " , " Laos " => " Laos " , " Latvia " => " Latvia " , " Lebanon " => " Lebanon " , " Lesotho " => " Lesotho " , " Liberia " => " Liberia " , " Libya " => " Libya " , " Liechtenstein " => " Liechtenstein " , " Lithuania " => " Lithuania " , " Luxembourg " => " Luxembourg " , " Macau SAR China " => " Macau SAR China " , " Macedonia " => " Macedonia " , " Madagascar " => " Madagascar " , " Malawi " => " Malawi " , " Malaysia " => " Malaysia " , " Maldives " => " Maldives " , " Mali " => " Mali " , " Malta " => " Malta " , " Marshall Islands " => " Marshall Islands " , " Martinique " => " Martinique " , " Mauritania " => " Mauritania " , " Mauritius " => " Mauritius " , " Mayotte " => " Mayotte " , " Metropolitan France " => " Metropolitan France " , " Mexico " => " Mexico " , " Micronesia " => " Micronesia " , " Midway Islands " => " Midway Islands " , " Moldova " => " Moldova " , " Monaco " => " Monaco " , " Mongolia " => " Mongolia " , " Montenegro " => " Montenegro " , " Montserrat " => " Montserrat " , " Morocco " => " Morocco " , " Mozambique " => " Mozambique " , " Myanmar [Burma] " => " Myanmar [Burma] " , " Namibia " => " Namibia " , " Nauru " => " Nauru " , " Nepal " => " Nepal " , " Netherlands " => " Netherlands " , " Netherlands Antilles " => " Netherlands Antilles " , " Neutral Zone " => " Neutral Zone " , " New Caledonia " => " New Caledonia " , " New Zealand " => " New Zealand " , " Nicaragua " => " Nicaragua " , " Niger " => " Niger " , " Nigeria " => " Nigeria " , " Niue " => " Niue " , " Norfolk Island " => " Norfolk Island " , " North Korea " => " North Korea " , " North Vietnam " => " North Vietnam " , " Northern Mariana Islands " => " Northern Mariana Islands " , " Norway " => " Norway " , " Oman " => " Oman " , " Pacific Islands Trust Territory " => " Pacific Islands Trust Territory " , " Pakistan " => " Pakistan " , " Palau " => " Palau " , " Palestinian Territories " => " Palestinian Territories " , " Panama " => " Panama " , " Panama Canal Zone " => " Panama Canal Zone " , " Papua New Guinea " => " Papua New Guinea " , " Paraguay " => " Paraguay " , " People's Democratic Republic of Yemen " => " People's Democratic Republic of Yemen " , " Peru " => " Peru " , " Philippines " => " Philippines " , " Pitcairn Islands " => " Pitcairn Islands " , " Poland " => " Poland " , " Portugal " => " Portugal " , " Puerto Rico " => " Puerto Rico " , " Qatar " => " Qatar " , " Romania " => " Romania " , " Russia " => " Russia " , " Rwanda " => " Rwanda " , " Réunion " => " Réunion " , " Saint Barthélemy " => " Saint Barthélemy " , " Saint Helena " => " Saint Helena " , " Saint Kitts and Nevis " => " Saint Kitts and Nevis " , " Saint Lucia " => " Saint Lucia " , " Saint Martin " => " Saint Martin " , " Saint Pierre and Miquelon " => " Saint Pierre and Miquelon " , " Saint Vincent and the Grenadines " => " Saint Vincent and the Grenadines " , " Samoa " => " Samoa " , " San Marino " => " San Marino " , " Saudi Arabia " => " Saudi Arabia " , " Senegal " => " Senegal " , " Serbia " => " Serbia " , " Serbia and Montenegro " => " Serbia and Montenegro " , " Seychelles " => " Seychelles " , " Sierra Leone " => " Sierra Leone " , " Singapore " => " Singapore " , " Slovakia " => " Slovakia " , " Slovenia " => " Slovenia " , " Solomon Islands " => " Solomon Islands " , " Somalia " => " Somalia " , " South Africa " => " South Africa " , " South Georgia and the South Sandwich Islands " => " South Georgia and the South Sandwich Islands " , " South Korea " => " South Korea " , " Spain " => " Spain " , " Sri Lanka " => " Sri Lanka " , " Sudan " => " Sudan " , " Suriname " => " Suriname " , " Svalbard and Jan Mayen " => " Svalbard and Jan Mayen " , " Swaziland " => " Swaziland " , " Sweden " => " Sweden " , " Switzerland " => " Switzerland " , " Syria " => " Syria " , " São Tomé and Príncipe " => " São Tomé and Príncipe " , " Taiwan " => " Taiwan " , " Tajikistan " => " Tajikistan " , " Tanzania " => " Tanzania " , " Thailand " => " Thailand " , " Timor-Leste " => " Timor-Leste " , " Togo " => " Togo " , " Tokelau " => " Tokelau " , " Tonga " => " Tonga " , " Trinidad and Tobago " => " Trinidad and Tobago " , " Tunisia " => " Tunisia " , " Turkey " => " Turkey " , " Turkmenistan " => " Turkmenistan " , " Turks and Caicos Islands " => " Turks and Caicos Islands " , " Tuvalu " => " Tuvalu " , " U.S. Minor Outlying Islands " => " U.S. Minor Outlying Islands " , " U.S. Miscellaneous Pacific Islands " => " U.S. Miscellaneous Pacific Islands " , " U.S. Virgin Islands " => " U.S. Virgin Islands " , " Uganda " => " Uganda " , " Ukraine " => " Ukraine " , " Union of Soviet Socialist Republics " => " Union of Soviet Socialist Republics " , " United Arab Emirates " => " United Arab Emirates " , " United Kingdom " => " United Kingdom " , " United States " => " United States " , " Unknown or Invalid Region " => " Unknown or Invalid Region " , " Uruguay " => " Uruguay " , " Uzbekistan " => " Uzbekistan " , " Vanuatu " => " Vanuatu " , " Vatican City " => " Vatican City " , " Venezuela " => " Venezuela " , " Vietnam " => " Vietnam " , " Wake Island " => " Wake Island " , " Wallis and Futuna " => " Wallis and Futuna " , " Western Sahara " => " Western Sahara " , " Yemen " => " Yemen " , " Zambia " => " Zambia " , " Zimbabwe " => " Zimbabwe " , " Åland Islands " => " Åland Islands " , );

        $countries = ['الكويت' , 'الامارات' , 'عمان', 'الأردن' , 'السعودية' , 'البحرين', 'مصر' , 'قطر', 'أبو ظبي', 'دبى'];

        $location = '';

        if ($seminar->id == 39) {
            $logo = 'seminar_design.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'logo', 'location'));
        }

        if ($seminar->id == 42) {
            $title='الخطوات الأولى للتداول فى الأسواق العاليمة';
            $logo = 'bashar2.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 43) {
            $logo = 'talal-desgin.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 44) {
            $logo = 'elmoltakaelawel.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 45) {
            $logo = 'zobda2.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 47) {
            $logo = 'zobda3.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 48) {
            $logo = 'zobda5.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 50) {
            $logo = 'zobda-oman.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }
        
        if ($seminar->id == 51) {
            $logo = 'zobda_emirates.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 52) {
            $logo = 'IMG-20190319-WA0018.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 58) {
            $location = 'أبو ظبى';
            $logo = 'warsha_abuzabi.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 59) {
            $location = 'الكويت';
            $logo = 'warsha_kuwait.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 60) {
            $location = 'أبو ظبي';
            $logo = "talal-zobda-abuzabi.jpg";
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 61) {
            $location = 'دبى';
            $logo = 'talal-zobda-dubai.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 62) {
            $location = 'البحرين';
            $logo = 'warsha_bahrain.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo', 'location' ));
        }

        if ($seminar->id == 64) {
            $logo = 'eman-aiaf (2).jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }
        

        if ($seminar->id == 68) {
        $countries = ['قطر' , 'الامارات' , 'عمان', 'الأردن' , 'السعودية' , 'البحرين', 'مصر' , 'الكويت', 'أبو ظبي', 'دبى'];
            $logo = 'WhatsApp Image 2019-07-14 at 3.57.48 PM.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }


        if ($seminar->id == 69) {
            $logo = 'IMG_1865.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 70) {
            $logo = 'IMG_1864.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 71) {
            $logo = '22.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 72) {
            $logo = '26.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 73) {
            $logo = '29.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 74) {
            $logo = 'IMG_1866.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 79) {
            $logo = 'WhatsApp Image 2019-11-19 at 12.56.22 PM.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 80) {
            $logo = 'WhatsApp Image 2019-11-19 at 12.56.23 PM.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 81) {
            $logo = 'Artboard 1 copy 6.jpg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        if ($seminar->id == 82) {
            $logo = 'WhatsApp Image 2019-11-12 at 7.19.44 PM.jpeg';
            return view('website.seminar4' , compact('countries' , 'seminar' , 'title' , 'logo' ));
        }

        return view('website.seminar' , compact('countries' , 'seminar' , 'location'));

    }

    public function stocks () {

        return view('website.stocks');

    }

    public function stocksUsers () {

        return view('website.stocks_users');

    }

}
