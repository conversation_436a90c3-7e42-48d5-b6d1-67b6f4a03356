<?php

namespace App\Http\Controllers\Website;

use App\Models\Client;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class EmployeeClientRegisterController extends Controller
{

    public $users = [
        ["email" => "<EMAIL>", "name" => "hawraa-almousawi"],
        ["email" => "<EMAIL>", "name" => "zahraa-almousawi"],
        ["email" => "<EMAIL>", "name" => "narjs-almousawi"],
        ["email" => "<EMAIL>", "name" => "bishayer-faraj"],
        ["email" => "<EMAIL>", "name" => "zeinab-nassar"],
        ["email" => "<EMAIL>", "name" => "najd-al<PERSON><PERSON><PERSON>"],
        ["email" => "<EMAIL>", "name" => "shooq-halawa"],
        ["email" => "<EMAIL>", "name" => "anwar-aldhafeeri"],
        ["email" => "<EMAIL>", "name" => "ahad-ghader"],
        ["email" => "<EMAIL>", "name" => "zahraa-altamemi"],
        ["email" => "<EMAIL>", "name" => "hiba-teshouri"],
        ["email" => "<EMAIL>", "name" => "khaled-kheir"],
        ["email" => "<EMAIL>", "name" => "aber-aldousari"],
        ["email" => "<EMAIL>", "name" => "shaikhah-alnassar"]
    ];

    public function employee ($by = null) {

        if ($by == null) {
            return redirect()->route('employee_client_register.employee', 'client-service');
        }

        if (!in_array($by, [
            'client-service',
            'mr-talal-department',
            'kafo-program',
            'vi-care',
            'lets-trade'
        ])) {
            abort(404);
        }

        return view('client_register', [
            'register_type' => Client::REGISTER_TYPE_EMPLOYEE,
            'by' => $by
        ]);

    }

    public function client ($by = null) {

        if ($by == null) {
            return redirect()->route('employee_client_register.client', 'client-service');
        }


        if (!in_array($by, array_merge(
            [
                'client-service',
                'mr-talal-department',
                'kafo-program',
                'vi-care',
                'lets-trade'
            ],
            array_column($this->users, 'name')
        ))) {
            abort(404);
        }

        return view('client_register', [
            'register_type' => Client::REGISTER_TYPE_CLIENT,
            'by' => $by
        ]);

    }

    public function register (Request $request) {

        $lang = $request->input('lang');

        $by = (string) $request->input('by');

        $registerType = $request->input('register_type');

        app()->setLocale($lang);

        $this->validate($request, [
            'first_name' => 'required|max:250',
            'last_name' => 'required|max:250',
            'phone_number' => 'required|max:250',
            'email' => 'required|max:250',
            'work_place' => 'required|max:250',
            'job_title' => 'required|max:250',
            'section' => 'required|max:250',
            'years_of_work' => 'required|max:250',
            'civil_id' => 'required',
            'passport' => 'required'
        ]);

        $files = ['civil_id', 'passport', 'proof_of_address'];

        try{

            $client = new Client();

            $client->fill($request->except($files));

            $paths = [];

            foreach ($files as $file) {

                $uploadedFile = $request->file($file);

                if (is_array($uploadedFile)) {

                    foreach ($uploadedFile as $singleUploadedFile) {

                        if ($path = $client->uploadFile($singleUploadedFile, $file)) {

                            $paths[] = $path;

                        }

                    }

                }else{

                    if ($path = $client->uploadFile($uploadedFile, $file)) {

                        $paths[] = $path;

                    }

                }

            }

            $client->save();

            $emailTo = '<EMAIL>';

            $cc = '';

            $subject = '';

            foreach ($this->users as $user) {
                if ($by == $user['name'] && $registerType == Client::REGISTER_TYPE_CLIENT) {
                    $emailTo = $user['email'];

                    $subject = 'Vi Markets (client service)';

                    break;
                }
            }

            if ($by == 'client-service' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Vi Markets (client service)';

            }

            if ($by == 'client-service' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>','<EMAIL>', '<EMAIL>'];

                $subject = 'Vi Markets (client service)';

            }

            if ($by == 'mr-talal-department' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Vi Markets (Mr.talal department)';

            }

            if ($by == 'mr-talal-department' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'Vi Markets (Mr.talal department)';

            }

            if ($by == 'eman-alyyaf-department' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Vi Markets (Eman Alayyaf department)';

            }

            if ($by == 'eman-alyyaf-department' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'Vi Markets (Eman Alayyaf department)';

            }

            if ($by == 'kafo-program' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = '';

                $subject = 'Kafo program';

            }

            if ($by == 'kafo-program' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'Kafo program';

            }

            if ($by == 'vi-care' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'OFM Kuwait (Follow up)';

            }

            if ($by == 'vi-care' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'OFM Kuwait (Follow up)';

            }

            if ($by == 'lets-trade' && $registerType == Client::REGISTER_TYPE_CLIENT) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'OFM Kuwait ( Oman Office )';
            }

            if ($by == 'lets-trade' && $registerType == Client::REGISTER_TYPE_EMPLOYEE) {

                $emailTo = '<EMAIL>';

                $cc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

                $subject = 'OFM Kuwait ( Oman Office )';

            }


//            if ($by == '1') {
//                $emailTo = '<EMAIL>';
//            }
//            if ($by == '2') {
//                $emailTo = '<EMAIL>';
//            }

            $subject .= ' ' . $client->first_name . ' ' . $client->last_name;

            $job = new \App\Jobs\SendClientRegisterEmail(
                $client,
                $registerType == Client::REGISTER_TYPE_CLIENT ?
                    $emailTo : '<EMAIL>',
//                '<EMAIL>',
                $paths,
                $cc,
                $subject
            );

            dispatch($job);

            return redirect()->back()->with('success', $lang == 'ar' ? 'شكرا لك' : 'Thank you');

        }catch (\Exception $exception) {

            Log::error($exception->getMessage());

            Log::error($exception->getTraceAsString());

            return redirect()->back()->with('failed', 'Something went wrong!');

        }

    }

    public function data ($register_type, $by = null) {

        $registrations = Client::query()->where('register_type', $register_type)->latest();

        if ($register_type == 'client' && $by != null) {
            $registrations = $registrations->where('by', $by);
        }elseif ($register_type == 'client' && $by == null) {
            $registrations = $registrations->where('by', '0');
        }

        $registrations = $registrations->paginate(30);

        return view('client_register_data', compact('registrations', 'register_type'));

    }

    public function download ($id, $path) {

//        $registration = Client::findOrFail($id);

//        $filePath = $registration->{$file};

        $path = decrypt($path);

        if (Storage::disk('public_storage')->exists($path)) {

            return Storage::disk('public_storage')->download(urldecode($path));

        }

        return redirect()->back();


    }

}

