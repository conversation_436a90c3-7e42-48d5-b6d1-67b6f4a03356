<?php

namespace App\Http\Controllers\Website;

use App\Models\KafoUser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class KafoController extends Controller
{

    public function index () {

        return response()->view('website.kafo');

    }

    public function register (Request $request) {

        $this->validate($request, [
            'name' => 'required',
            'phone' => 'required'
        ]);

        $user = KafoUser::create(
            array_merge(
                $request->except('cv'),
                ['cv' => $this->uploadCV($request)]
            )
        );


        return redirect()->back()->with('success' , 'تم الأرسال بنجاح');

    }

    private function uploadCV (Request $request) {

        if ($file = $request->file('cv')) {

            $file_name = str_replace(' ', '_', $request->input('name')) .
            '_CV' . uniqid() . '.' . $file->getClientOriginalExtension();

            $file->move(public_path('storage/kafo_cv'), $file_name);

            return $file_name;

        }

        return null;

    }

}
