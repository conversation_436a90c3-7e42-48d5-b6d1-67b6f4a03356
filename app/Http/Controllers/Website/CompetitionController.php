<?php

namespace App\Http\Controllers\Website;

use App\Models\CompetitionParticipant;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class CompetitionController extends Controller
{

    protected $competitionParticipant;

    public function __construct(CompetitionParticipant $competitionParticipant)
    {
        $this->competitionParticipant = $competitionParticipant;
    }

    public function sevenBars ($lang = 'en') {

        if ($lang == 'timer') {
            return view('website.timer');
        }

        return view('website.7bars' , compact('lang'));

    }

    public function register (Request $request) {

        $validator = Validator::make($request->all() , [
            'first_name' => 'required' ,
            'last_name' => 'required' ,
            // 'telephone' => 'required|unique:competition_participants' 
        ]);

        if ($validator->fails()) {

            return redirect()->back()->withInput($request->only([
                'first_name' , 'last_name' , 'email' , 'telephone'
            ]))->withErrors($validator);

        }

        $this->competitionParticipant->create($request->all());

        return redirect()->back()->with('success' , 'You have registered successfully');

    }

    public function timer () {

        return view('website.timer');

    }

    public function participants () {

        $participants = $this->competitionParticipant->where('id' , '>' , 1856)->latest()->get();

        return view('website.competition_participants' , compact('participants'));

    }

}
