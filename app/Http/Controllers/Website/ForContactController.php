<?php

namespace App\Http\Controllers\Website;

use App\Models\Seminar;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ForContactController extends Controller
{

    protected $seminar;

    protected $names = [
        'Talal-Alajmi' => [
            'en' => 'Talal-Alajmi',
            'ar' => 'طلال العجمى'
        ]
    ];

    public function __construct(Seminar $seminar)
    {
        $this->seminar = $seminar;
    }

    public function index ($name) {

        $logo = '';

        $countries = [
            'الكويت' , 'الامارات' , 'عمان',
            'الأردن' , 'السعودية' , 'البحرين', 'مصر' ,
            'قطر', 'أبو ظبي', 'دبى' ,
            'الجزائر', 'السودان', 'الصومال', 'العرق', 'المغرب', 'اليمن',
            'جزر القمر', 'جيبوتى', 'سوريا' ,'فلسطين',
            'لبنان', 'ليبيا', 'موريتانيا',
        ];

        $seminar = $this->seminar->where('title', $name)->first();

        if ($seminar->title == 'Talal-Alajmi') {
            $logo = 'talal_for_contact (2).jpg';
            return view('website.for_contact' , compact('countries' , 'seminar' , 'logo' ));
        }

        if ($seminar->title == 'Ghaneemah-Alameri') {
            $logo = 'Ghaneemah-Alameri (2).jpg';
            return view('website.for_contact' , compact('countries' , 'seminar' , 'logo' ));
        }

        if ($seminar->title == 'Eman-Alayyaf') {
            $logo = 'inq.jpg';
            return view('website.for_contact' , compact('countries' , 'seminar' , 'logo' ));
        }

        return view('website.for_contact' , compact('countries' , 'seminar' , 'logo' ));

    }

    public function participants ($name) {

        $seminar = $this->seminar->where('title', $name)->first();

        $participants = $seminar->participants;

        return view(
            'website.for_contacts_participants',
            compact('participants')
        );

    }

}
