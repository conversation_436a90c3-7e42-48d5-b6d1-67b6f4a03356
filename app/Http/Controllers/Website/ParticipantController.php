<?php

namespace App\Http\Controllers\Website;

use App\Models\Participant;
use App\Models\Seminar;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class ParticipantController extends Controller
{

    protected $participant;
    protected $seminar;

    public function __construct (Participant $participant , Seminar $seminar)
    {

        $this->participant = $participant;
        $this->seminar = $seminar;

    }

    public function register (Request $request)
    {

        // if($this->participant->where([
        //     ['seminar_id' , $request->input('seminar_id')] ,
        //     ['email' , $request->input('email')]
        // ])->first()) {
        //     return redirect()->back()->with('failed' , 'البريد الألكترونى مسجل بالفعل');
        // }

        if($this->participant->where([
            ['seminar_id' , $request->input('seminar_id')] ,
            ['phone' , $request->input('phone')]
        ])->first()) {
            return redirect()->back()->with('failed' , 'رقم الهاتف مسجل بالفعل');
        }


        $this->validate($request , [
            'first_name' => 'required|max:20' ,
            'last_name' => 'required|max:20' ,
            // 'email' => 'required|email' ,
//            'country' => 'required' ,
            'phone' => 'required|numeric' ,
        ]);

        $data = $request->all();

        $this->participant->create($data);

        $seminar = $this->seminar->find($request->input('seminar_id'));

        return redirect()->back()->with('success' , $this->getMessage($seminar));

    }

    private function getMessage ($seminar) {

        if ($seminar->id == 48) {
            return 'تم التسجيل بنجاح فى الندوة وبأنتظاركم فى فندق توليب قاعة كريستال الساعة 5 مساء';
        }else{
            return 'تم التسجيل بنجاح فى الندوة';
        }

    }

}
