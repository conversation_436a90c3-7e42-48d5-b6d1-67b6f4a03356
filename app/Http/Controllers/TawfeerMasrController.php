<?php

namespace App\Http\Controllers;

use App\Models\TawfeerMasrOffer;
use Illuminate\Http\Request;

class TawfeerMasrController extends Controller
{

    public function __construct()
    {

        app()->setLocale('ar');

    }

    public function offers ()
    {

        return view('tawfeer-masr.offers');

    }

    public function store (Request $request)
    {

        $this->validate($request, [
            'title' => ['required'],
            'date' => ['required'],
            'images' => ['required'],
        ], [], [
            'title' => 'العنوان',
            'images' => 'الصور'
        ]);

        $offer = new TawfeerMasrOffer();
        $offer->fill($request->all());
        $offer->save();

        return redirect()->back()->with('success', 'تم الحفظ بنجاح');

    }

    public function delete (Request $request, $id)
    {

        TawfeerMasrOffer::query()->findOrFail($id)->forceDelete();

        return redirect()->back();

    }


}
