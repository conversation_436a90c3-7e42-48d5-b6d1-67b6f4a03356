<?php

namespace App\Http\Controllers;

use App\Models\KafoUser;
use Carbon\Carbon;
use Illuminate\Http\Request;

class SeminarsController extends Controller
{

    protected $dates = [];

    public function __construct()
    {
        $seminarTitles = [
            'bashar10days' => 'أساسيات التداول من الألف الى الياء',
            'basharstocks' => 'المضاربة اليومية بالأسهم الامريكية',
            'bashar' => 'اساسيات التداول مع أ. بشار العصفور',
            'bashar1' => 'أساسيات التحليل الفني',
            'bashar2' => 'دورة إعداد متداول حتى الإتقان',
            'bashar3' => 'شرح مبادىء ادوات التحليل الفني',
            'bashar4' => 'استراتيجية المحور الاساسي',
            'bashar5' => 'استراتيجية Prime Mate',
            'bashar-ttc' => 'اساسيات التداول في الاسواق العالمية من البداية الاحتراف',
            'bashar-dubai' => 'دورة إعداد متداول حتى الإتقان',
            'sheika' => 'إدارة المخاطر',
            'sheika1' => 'الناسخ',
            'sheika2' => 'مدخل الى الأسهم',
            'sheika&ranim' => 'حلقة نقاشية',
            'bashar&ahmed' => 'تحليل حي لاجتماع الفيدرالي',
            'bashar&mushari' => 'تطبيق الاسواق العالمية والمضاربة في الأسهم',
            'may&ahmed' => 'تحليل فرص السوق',
            'talal&ahmed' => 'الفرص الاستثمارية أثناء الأزمات العالمية',
            '9yearsegypt' => 'VI MARKETS بمرور ٩ سنوات من النجاح في مصر احتفال',
            'm&m' => 'استراتيجية بالتداول',
            'm&m1' => 'إدارة مخاطر',
            'm&m2' => 'اندكيتر أ. طلال العجمي ',
            'm&m3' => 'حلقة نقاشية',
            'm&m4' => 'حلقة نقاشية',
            'live' => 'تحليل حي لاجتماع الفيدرالي الأمريكي',
            'ranim' => 'الايشيموكو',
            'ranim1' => 'الدايفرجنس',
            'ranim2' => 'قراءة المؤشرات الاقتصادية',
            'ranim3' => 'الايشيموكو',
            'ahmed-moaty' => 'السلوك السعري',
            'ahmed-moaty1' => 'التحليل الأساسي',
            'ahmed-moaty2' => 'طريقتي في التداول',
            'ahmed-moaty3' => 'التحليل الأساسي',
            'rania' => 'التحليل الفني',
            'rania1' => 'أساسيات التداول من الالف الى الياء',
            'rania2' => 'منصة التداول Mt4',
            'rania3' => 'منصة التداول MT4',
            'talal' => 'ورشة تطبيق عملي',
            'talal&marc' => 'الاسهم',
            'hashem' => 'استراتيجية في التداول',
            'hashem1' => 'الخطوات الأولى للدخول بمجال التداول',
            'hashem2' => 'استراتيجية في التداول',
            'hashem3' => 'استراتيجية بالتداول',
            'may' => 'مؤشر أسعار المستهلكين CPI',
            'may&ahmed&bashar' => 'الأسواق في أسبوع وماذا بعد الفيدرالي؟ ',
            'trading-seminar-dubai' => 'تداول الأسواق العالمية دبي',
            'trading-seminar' => 'الأسواق العالمية وبرنامج التمويل Axi Select',
            'trading-seminar-bahrain' => 'الأسواق العالمية وبرنامج التمويل Axi Select',
            'trading-seminar-oman' => 'تداول الأسواق العالمية عمان',
            'trading-seminar-egypt' => 'تداول الأسواق العالمية مصر',
            'mazin' => 'التحليل الفني',
            'vi-markets' => 'مقدمة حول VI MARKETS',
            'viaxi' => 'أساسيات التداول من الألف إلى الياء',
            'vi&axi' => 'دورات مجانية على مدار الأسبوع',
            'vi&axi1' => 'دورات مجانية على مدار الأسبوع',
            'rabi' => 'الحسابات الممولة',
            'nadeem' => 'Basics of trading ( for beginners )',
            'mushari' => 'التداول في سوق الأسهم',
            'khatra-baloshi' => 'التحليل الفني',
            'khatra-baloshi1' => 'إدارة المخاطر',
            'khatra-baloshi2' => 'إدارة المخاطر',
            'walid' => 'سيكلوجية التداول',
            'shereen' => 'إدارة المخاطر',
            'shereen1' => 'استراتيجية الاستوكاستك',
            'shereen2' => 'استراتيجية الاستوكاستك',
            'zainab' => 'أساسيات التداول',
            'zainab1' => 'إدارة المخاطر',
            'zainab2' => 'تعامل مع صفقاتك باحترافية',
            'zainab3' => 'تعامل مع صفقاتك باحترافية',
            'mosaed' => 'ICT من الصفر الى الاحتراف',
            'mosaed1' => 'ICT من الصفر الى الاحتراف',
        ];

        $this->dates = [
            'bashar10days' => '2024-04-17',
            'basharstocks' => '2024-05-13',
            'bashar' => '2025-08-10',
            'bashar1' => '2023-05-01',
            'bashar2' => '2025-03-04',
            'bashar3' => '2022-12-05',
            'bashar4' => '2022-12-05',
            'bashar5' => '2021-12-05',
            'bashar-ttc' => '2023-03-09',
            'bashar-dubai' => '2024-09-08',
            'bashar&ahmed' => '2025-07-28',
            'bashar&mushari' => '2025-02-16',
            'may&ahmed' => '2023-10-31',
            'talal&ahmed' => '2022-07-06',
            '9yearsegypt' => '2025-05-05',
            'm&m' => '2024-11-05',
            'm&m1' => '2024-08-08',
            'm&m2' => '2024-08-08',
            'm&m3' => '2023-11-29',
            'm&m4' => '2022-11-29',
            'sheika' => '2025-04-06',
            'sheika1' => '2023-07-31',
            'sheika2' => '2023-07-31',
            'sheika&ranim' => '2024-03-14',
            'ahmed-moaty' => '2025-06-02',
            'ahmed-moaty1' => '2025-08-03',
            'ahmed-moaty2' => '2025-08-03',
            'ahmed-moaty3' => '2024-10-02',
            'live' => '2023-02-27',
            'ranim' => '2025-07-28',
            'ranim1' => '2025-06-03',
            'ranim2' => '2025-07-07',
            'ranim3' => '2022-10-26',
            'rania' => '2025-08-03',
            'rania1' => '2025-05-01',
            'rania2' => '2024-10-31',
            'rania3' => '2022-08-29',
            'talal' => '2024-02-20',
            'talal&marc' => '27-07-2022',
            'hashem' => '2025-02-04',
            'hashem1' => '2024-12-03',
            'hashem2' => '2025-03-04',
            'hashem3' => '2024-12-03',
            'may' => '2023-08-28',
            'may&ahmed&bashar' => '2023-02-27',
            'trading-seminar-dubai' => '2023-11-05',
            'trading-seminar' => '2024-08-29',
            'trading-seminar-bahrain' => '2024-08-29',
            'trading-seminar-oman' => '2023-11-07',
            'trading-seminar-egypt' => '2023-11-07',
            'mazin' => '2023-12-03',
            'vi-markets' => '2024-01-16',
            'viaxi' => '2024-01-16',
            'vi&axi' => '2024-01-16',
            'vi&axi1' => '2024-01-31',
            'rabi' => '2024-05-10',
            'nadeem' => '2024-03-14',
            'mushari' => '2025-08-03',
            'khatra-baloshi' => '2025-02-04',
            'khatra-baloshi1' => '2024-12-04',
            'khatra-baloshi2' => '2024-12-04',
            'walid' => '2024-12-31',
            'shereen' => '2025-07-07',
            'shereen1' => '2025-07-28',
            'shereen2' => '2025-08-03',
            'zainab' => '2025-04-03',
            'zainab1' => '2025-05-08',
            'zainab2' => '2025-05-06',
            'zainab3' => '2025-03-04',
            'mosaed' => '2025-07-31',
            'mosaed1' => '2025-08-03',
        ];


        $events = [
            'bashar' => '2023-09-06',
            'bashar1' => ['2023-05-14', '2023-05-15', '2023-05-16'],
            'bashar2' => '2022-12-28',
            'bashar3' => '2022-12-19',
            'bashar4' => '2021-12-20',
            'bashar5' => '2021-12-05',
            'bashar-ttc' => ['2023-03-12', '2023-03-13', '2023-03-14', '2023-03-15', '2023-03-16'],
            'bashar&ahmed' => '2023-11-01',
            'may&ahmed' => '2023-11-05',
            'talal&ahmed' => '2022-07-06',
            'mohammad' => ['2023-07-09', '2023-07-10'],
            'm&m' => ['2023-06-25', '2023-06-26'],
            'm&m1' => '2024-03-01',
            'm&m2' => '2023-10-15',
            'm&m3' => '2022-10-22',
            'm&m4' => ['2023-10-23', '2023-10-24'],
            'ahmed-moaty' => '2023-09-18',
            'ahmed-moaty1' => '2023-10-16',
            'ahmed-moaty2' => '2023-10-19',
            'ahmed-moaty3' => '2022-03-15',
            'live' => '2023-03-22',
            'ranim' => '2024-02-07',
            'ranim1' => '2024-04-07',
            'ranim2' => '2025-03-19',
            'ranim3' => '2022-11-24',
            'rania' => '2023-11-07',
            'rania1' => '2022-11-30',
            'rania2' => '2022-06-01',
            'rania3' => '2022-09-28',
            'talal' => '2023-02-01',
            'talal&marc' => '27-07-2022',
            'hashem' => '2023-10-18',
            'hashem1' => '2023-09-12',
            'hashem2' => '2023-09-28',
            'sheika' => '2024-02-07',
            'sheika1' => '2023-08-13',
            'sheika2' => '2023-08-14',
            'may' => '2023-10-29',
            'may&ahmed&bashar' => '2023-03-26',
        ];


        view()->share('seminarTitles', $seminarTitles);


        $newEvents = [];

        foreach ($events as $key => $date) {
            if (is_array($date)) {
                foreach ($date as $dateItem) {
                    $newEvents[] = [
                        'start' => $dateItem,
                        'end' => $dateItem,
                        'url' => '/seminars/trading-course/' . $key,
                        'day' => Carbon::parse($dateItem)->day
                    ];
                }
            }else{
                $newEvents[] = [
                    'start' => $date,
                    'end' => $date,
                    'url' => '/seminars/trading-course/' . $key,
                    'day' => Carbon::parse($date)->day
                ];
            }
        }

        $newEvents = collect($newEvents)->filter(function ($event) {
            return now()->lte(Carbon::parse($event['start']));
        })->values()->toArray();

        view()->share('seminarEvents', $newEvents);

    }

    public function show ($presenter = null)
    {

        if ($presenter) {

            $view = str_replace('-', '_', $presenter);
            if (view()->exists('new_seminars.' . $view)) return view('new_seminars.' . $view, compact('presenter'));
            $view = 'seminars.' . $view;
            return view($view, compact('presenter'));

        }

        return view('seminar2');

    }

    public function register (Request $request)
    {

        $this->validate($request, [
            'name' => 'required|max:250',
            'phone' => 'required|max:250',
            'email' => 'required|max:250|email',
            'dial_code' => 'required|max:250',
        ]);

        KafoUser::query()->create(
            $request->merge([
                'phone' => $request->input('dial_code') . $request->input('phone'),
                'is_client' => $request->input('is_client') ?? 0
            ])->all()
        );

        return redirect()->route('seminars.success')->with('success', 'شكرا لتسجيلك معنا');

    }

    public function participants (Request $request, $presenter = null)
    {

        $dates = $this->dates;

        $presenters = array_filter(array_keys($dates), function ($currentPresenter) use ($presenter) {
//            $presenter = preg_replace('/\d*/', '' , $presenter);
//            $currentPresenter = preg_replace('/\d*/', '' , $currentPresenter);
            return $presenter == $currentPresenter;
        });

        $users = collect([]);

        foreach ($presenters as $currentPresenter) {

            $query = KafoUser::query()->latest()
                ->where('presenter', $currentPresenter)
                ->where('created_at', '>=', Carbon::parse(data_get($dates, $currentPresenter) ?? '2021-08-25'));

            $users = $query->get()->merge($users)->unique('phone')->unique('email');

        }

        return view('seminar_participants', compact('users', 'presenter'));

    }

    public function success ()
    {
        return view('new_seminars.success');
    }

}
